
> ai-text-game-frontend@0.1.0 dev:debug
> vite --logLevel info --debug

2025-08-05T02:34:03.798Z vite:config bundled config file loaded in 141.29ms
2025-08-05T02:34:03.816Z vite:config using resolved config: {
  plugins: [
    'vite:optimized-deps',
    'vite:watch-package-data',
    'vite:pre-alias',
    'alias',
    'vite:react-babel',
    'vite:react-refresh',
    'vite:react-jsx',
    'vite:modulepreload-polyfill',
    'vite:resolve',
    'vite:html-inline-proxy',
    'vite:css',
    'vite:esbuild',
    'vite:json',
    'vite:wasm-helper',
    'vite:worker',
    'vite:asset',
    'vite:wasm-fallback',
    'vite:define',
    'vite:css-post',
    'vite:worker-import-meta-url',
    'vite:asset-import-meta-url',
    'vite:dynamic-import-vars',
    'vite:import-glob',
    'vite:client-inject',
    'vite:import-analysis'
  ],
  define: {
    __DEV_MODE_ENABLED__: true,
    'process.env.DISABLE_DEV_MODE': 'false'
  },
  resolve: {
    mainFields: [ 'module', 'jsnext:main', 'jsnext' ],
    browserField: true,
    conditions: [],
    extensions: [
      '.mjs',  '.js',
      '.mts',  '.ts',
      '.jsx',  '.tsx',
      '.json'
    ],
    dedupe: [ 'react', 'react-dom' ],
    preserveSymlinks: false,
    alias: [ [Object], [Object], [Object] ]
  },
  server: {
    preTransformRequests: true,
    port: 3000,
    host: true,
    proxy: { '/api': [Object] },
    logLevel: 'info',
    sourcemapIgnoreList: [Function: isInNodeModules],
    middlewareMode: false,
    fs: { strict: true, allow: [Array], deny: [Array] }
  },
  build: {
    target: [ 'es2020', 'edge88', 'firefox78', 'chrome87', 'safari14' ],
    cssTarget: [ 'es2020', 'edge88', 'firefox78', 'chrome87', 'safari14' ],
    outDir: '../static/dist',
    assetsDir: 'assets',
    assetsInlineLimit: 4096,
    cssCodeSplit: true,
    sourcemap: true,
    rollupOptions: { output: [Object] },
    minify: 'esbuild',
    terserOptions: {},
    write: true,
    emptyOutDir: true,
    copyPublicDir: true,
    manifest: false,
    lib: false,
    ssr: false,
    ssrManifest: false,
    ssrEmitAssets: false,
    reportCompressedSize: true,
    chunkSizeWarningLimit: 500,
    watch: null,
    commonjsOptions: { include: [Array], extensions: [Array] },
    dynamicImportVarsOptions: { warnOnError: true, exclude: [Array] },
    modulePreload: { polyfill: true },
    cssMinify: true
  },
  logLevel: 'info',
  optimizeDeps: {
    disabled: 'build',
    force: undefined,
    include: [ 'react/jsx-runtime', 'react/jsx-dev-runtime', 'react' ],
    esbuildOptions: { preserveSymlinks: false }
  },
  esbuild: {
    jsxDev: true,
    jsx: 'automatic',
    jsxImportSource: undefined,
    jsxSideEffects: false
  },
  configFile: '/root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/web/frontend/vite.config.ts',
  configFileDependencies: [
    '/root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/web/frontend/vite.config.ts'
  ],
  inlineConfig: {
    root: undefined,
    base: undefined,
    mode: undefined,
    configFile: undefined,
    logLevel: 'info',
    clearScreen: undefined,
    optimizeDeps: { force: undefined },
    server: { host: undefined }
  },
  root: '/root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/web/frontend',
  base: '/',
  rawBase: '/',
  publicDir: '/root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/web/frontend/public',
  cacheDir: '/root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/web/frontend/node_modules/.vite',
  command: 'serve',
  mode: 'development',
  ssr: {
    format: 'esm',
    target: 'node',
    optimizeDeps: { disabled: true, esbuildOptions: [Object] }
  },
  isWorker: false,
  mainConfig: null,
  isProduction: false,
  css: undefined,
  preview: {
    port: undefined,
    strictPort: undefined,
    host: true,
    allowedHosts: undefined,
    https: undefined,
    open: undefined,
    proxy: { '/api': [Object] },
    cors: undefined,
    headers: undefined
  },
  envDir: '/root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/web/frontend',
  env: {
    VITE_APP_ENV: 'development',
    VITE_API_BASE_URL: 'http://localhost:8080',
    VITE_API_TIMEOUT: '30000',
    VITE_DEV_MODE: 'true',
    VITE_ENABLE_DEV_TOOLS: 'true',
    VITE_SHOW_DEV_INDICATOR: 'true',
    VITE_SKIP_AUTH: 'true',
    VITE_AUTO_LOGIN: 'true',
    VITE_DEBUG_LOGS: 'true',
    VITE_VERBOSE_LOGS: 'true',
    VITE_ENABLE_MOCK: 'false',
    VITE_MOCK_DELAY: '500',
    VITE_HMR_PORT: '3001',
    VITE_HMR_HOST: 'localhost',
    VITE_USER_NODE_ENV: 'development',
    BASE_URL: '/',
    MODE: 'development',
    DEV: true,
    PROD: false
  },
  assetsInclude: [Function: assetsInclude],
  logger: {
    hasWarned: false,
    info: [Function: info],
    warn: [Function: warn],
    warnOnce: [Function: warnOnce],
    error: [Function: error],
    clearScreen: [Function: clearScreen],
    hasErrorLogged: [Function: hasErrorLogged]
  },
  packageCache: Map(1) {
    'fnpd_/root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/web/frontend' => {
      dir: '/root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/web/frontend',
      data: [Object],
      hasSideEffects: [Function: hasSideEffects],
      webResolvedImports: {},
      nodeResolvedImports: {},
      setResolvedCache: [Function: setResolvedCache],
      getResolvedCache: [Function: getResolvedCache]
    },
    set: [Function (anonymous)]
  },
  createResolver: [Function: createResolver],
  worker: {
    format: 'iife',
    plugins: [
      'vite:optimized-deps',
      'vite:watch-package-data',
      'vite:pre-alias',
      'alias',
      'vite:modulepreload-polyfill',
      'vite:resolve',
      'vite:html-inline-proxy',
      'vite:css',
      'vite:esbuild',
      'vite:json',
      'vite:wasm-helper',
      'vite:worker',
      'vite:asset',
      'vite:wasm-fallback',
      'vite:define',
      'vite:css-post',
      'vite:worker-import-meta-url',
      'vite:asset-import-meta-url',
      'vite:dynamic-import-vars',
      'vite:import-glob',
      'vite:client-inject',
      'vite:import-analysis'
    ],
    rollupOptions: {},
    getSortedPlugins: [Function: getSortedPlugins],
    getSortedPluginHooks: [Function: getSortedPluginHooks]
  },
  appType: 'spa',
  experimental: { importGlobRestoreExtension: false, hmrPartialAccept: false },
  webSocketToken: 'UxEdBA63XWx2',
  additionalAllowedHosts: [],
  getSortedPlugins: [Function: getSortedPlugins],
  getSortedPluginHooks: [Function: getSortedPluginHooks]
}
2025-08-05T02:34:03.826Z vite:deps Hash is consistent. Skipping. Use --force to override.

  VITE v4.5.14  ready in 273 ms

  ➜  Local:   http://localhost:3000/
  ➜  Network: http://*************:3000/
  ➜  press h to show help
2025-08-05T02:34:03.829Z vite:esbuild 15.69ms tsconfck init /root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/web/frontend
2025-08-05T02:34:05.477Z vite:html-fallback Rewriting GET / to /index.html
2025-08-05T02:34:05.493Z vite:resolve 1.10ms /src/main.tsx -> /root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/web/frontend/src/main.tsx
2025-08-05T02:34:05.493Z vite:resolve 0.89ms /index.html?html-proxy&direct&index=0.css -> /root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/web/frontend/index.html?html-proxy&direct&index=0.css
2025-08-05T02:34:05.497Z vite:import-analysis [skipped] index.html?html-proxy&direct&index=0.css
2025-08-05T02:34:05.497Z vite:time 23.58ms /index.html
2025-08-05T02:34:05.500Z vite:load 5.18ms [fs] /src/main.tsx
2025-08-05T02:34:05.548Z vite:resolve 0.31ms react/jsx-dev-runtime ->  react/jsx-dev-runtime
2025-08-05T02:34:05.549Z vite:resolve 1.21ms ./store -> /root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/web/frontend/src/store/index.ts
2025-08-05T02:34:05.549Z vite:resolve 1.23ms ./App -> /root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/web/frontend/src/App.tsx
2025-08-05T02:34:05.549Z vite:resolve 1.26ms ./components/AppInitializer -> /root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/web/frontend/src/components/AppInitializer.tsx
2025-08-05T02:34:05.549Z vite:resolve 1.29ms ./styles/index.css -> /root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/web/frontend/src/styles/index.css
2025-08-05T02:34:05.549Z vite:resolve 1.53ms react -> /root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/web/frontend/node_modules/.vite/deps/react.js?v=5981b4ff
2025-08-05T02:34:05.549Z vite:resolve 1.54ms react-dom/client -> /root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/web/frontend/node_modules/.vite/deps/react-dom_client.js?v=5981b4ff
2025-08-05T02:34:05.549Z vite:resolve 1.55ms react-redux -> /root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/web/frontend/node_modules/.vite/deps/react-redux.js?v=5981b4ff
2025-08-05T02:34:05.549Z vite:resolve 1.57ms react-router-dom -> /root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/web/frontend/node_modules/.vite/deps/react-router-dom.js?v=5981b4ff
2025-08-05T02:34:05.549Z vite:resolve 1.58ms antd -> /root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/web/frontend/node_modules/.vite/deps/antd.js?v=5981b4ff
2025-08-05T02:34:05.549Z vite:resolve 1.59ms antd/locale/zh_CN -> /root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/web/frontend/node_modules/.vite/deps/antd_locale_zh_CN.js?v=5981b4ff
2025-08-05T02:34:05.550Z vite:load 1.02ms [plugin]  react/jsx-dev-runtime
2025-08-05T02:34:05.550Z vite:import-analysis /node_modules/.vite/deps/react.js?v=5981b4ff needs interop
2025-08-05T02:34:05.552Z vite:import-analysis /node_modules/.vite/deps/react-dom_client.js?v=5981b4ff needs interop
2025-08-05T02:34:05.552Z vite:import-analysis /node_modules/.vite/deps/antd_locale_zh_CN.js?v=5981b4ff needs interop
2025-08-05T02:34:05.553Z vite:optimize-deps load /root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/web/frontend/node_modules/.vite/deps/react.js
2025-08-05T02:34:05.553Z vite:optimize-deps load /root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/web/frontend/node_modules/.vite/deps/react-dom_client.js
2025-08-05T02:34:05.553Z vite:optimize-deps load /root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/web/frontend/node_modules/.vite/deps/react-redux.js
2025-08-05T02:34:05.553Z vite:optimize-deps load /root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/web/frontend/node_modules/.vite/deps/react-router-dom.js
2025-08-05T02:34:05.553Z vite:optimize-deps load /root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/web/frontend/node_modules/.vite/deps/antd.js
2025-08-05T02:34:05.553Z vite:optimize-deps load /root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/web/frontend/node_modules/.vite/deps/antd_locale_zh_CN.js
2025-08-05T02:34:05.554Z vite:import-analysis 7.30ms [11 imports rewritten] src/main.tsx
2025-08-05T02:34:05.556Z vite:transform 55.99ms /src/main.tsx
2025-08-05T02:34:05.557Z vite:resolve 0.16ms react/jsx-dev-runtime -> /root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/web/frontend/node_modules/.vite/deps/react_jsx-dev-runtime.js?v=5981b4ff
2025-08-05T02:34:05.557Z vite:import-analysis /node_modules/.vite/deps/react_jsx-dev-runtime.js?v=5981b4ff needs interop
2025-08-05T02:34:05.557Z vite:optimize-deps load /root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/web/frontend/node_modules/.vite/deps/react_jsx-dev-runtime.js
2025-08-05T02:34:05.557Z vite:import-analysis 0.77ms [1 imports rewritten]  react/jsx-dev-runtime
2025-08-05T02:34:05.557Z vite:transform 6.93ms  react/jsx-dev-runtime
2025-08-05T02:34:05.559Z vite:load 6.20ms [plugin] /node_modules/.vite/deps/react.js?v=5981b4ff
2025-08-05T02:34:05.559Z vite:resolve 0.17ms ./chunk-LXGCQ6UQ.js -> /root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/web/frontend/node_modules/.vite/deps/chunk-LXGCQ6UQ.js?v=5981b4ff
2025-08-05T02:34:05.559Z vite:resolve 0.19ms ./chunk-ROME4SDB.js -> /root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/web/frontend/node_modules/.vite/deps/chunk-ROME4SDB.js?v=5981b4ff
2025-08-05T02:34:05.559Z vite:optimize-deps load /root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/web/frontend/node_modules/.vite/deps/chunk-LXGCQ6UQ.js
2025-08-05T02:34:05.559Z vite:optimize-deps load /root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/web/frontend/node_modules/.vite/deps/chunk-ROME4SDB.js
2025-08-05T02:34:05.559Z vite:import-analysis 0.48ms [2 imports rewritten] node_modules/.vite/deps/react.js?v=5981b4ff
2025-08-05T02:34:05.559Z vite:transform 0.61ms /node_modules/.vite/deps/react.js?v=5981b4ff
2025-08-05T02:34:05.560Z vite:load 6.86ms [plugin] /node_modules/.vite/deps/react-dom_client.js?v=5981b4ff
2025-08-05T02:34:05.560Z vite:resolve 0.09ms ./chunk-ZRNALROW.js -> /root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/web/frontend/node_modules/.vite/deps/chunk-ZRNALROW.js?v=5981b4ff
2025-08-05T02:34:05.560Z vite:optimize-deps load /root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/web/frontend/node_modules/.vite/deps/chunk-ZRNALROW.js
2025-08-05T02:34:05.560Z vite:import-analysis 0.48ms [3 imports rewritten] node_modules/.vite/deps/react-dom_client.js?v=5981b4ff
2025-08-05T02:34:05.560Z vite:transform 0.58ms /node_modules/.vite/deps/react-dom_client.js?v=5981b4ff
2025-08-05T02:34:05.560Z vite:load 7.53ms [plugin] /node_modules/.vite/deps/react-redux.js?v=5981b4ff
2025-08-05T02:34:05.561Z vite:resolve 0.14ms ./chunk-J2UTGV2I.js -> /root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/web/frontend/node_modules/.vite/deps/chunk-J2UTGV2I.js?v=5981b4ff
2025-08-05T02:34:05.561Z vite:resolve 0.16ms ./chunk-HJ4OHFNV.js -> /root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/web/frontend/node_modules/.vite/deps/chunk-HJ4OHFNV.js?v=5981b4ff
2025-08-05T02:34:05.561Z vite:resolve 0.18ms ./chunk-SNXB62YR.js -> /root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/web/frontend/node_modules/.vite/deps/chunk-SNXB62YR.js?v=5981b4ff
2025-08-05T02:34:05.561Z vite:optimize-deps load /root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/web/frontend/node_modules/.vite/deps/chunk-J2UTGV2I.js
2025-08-05T02:34:05.561Z vite:optimize-deps load /root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/web/frontend/node_modules/.vite/deps/chunk-HJ4OHFNV.js
2025-08-05T02:34:05.561Z vite:optimize-deps load /root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/web/frontend/node_modules/.vite/deps/chunk-SNXB62YR.js
2025-08-05T02:34:05.561Z vite:import-analysis 0.67ms [6 imports rewritten] node_modules/.vite/deps/react-redux.js?v=5981b4ff
2025-08-05T02:34:05.561Z vite:transform 0.76ms /node_modules/.vite/deps/react-redux.js?v=5981b4ff
2025-08-05T02:34:05.561Z vite:load 8.28ms [plugin] /node_modules/.vite/deps/antd_locale_zh_CN.js?v=5981b4ff
2025-08-05T02:34:05.562Z vite:import-analysis 0.53ms [1 imports rewritten] node_modules/.vite/deps/antd_locale_zh_CN.js?v=5981b4ff
2025-08-05T02:34:05.562Z vite:transform 0.65ms /node_modules/.vite/deps/antd_locale_zh_CN.js?v=5981b4ff
2025-08-05T02:34:05.562Z vite:load 11.90ms [fs] /src/store/index.ts
2025-08-05T02:34:05.562Z vite:load 9.43ms [plugin] /node_modules/.vite/deps/react-router-dom.js?v=5981b4ff
2025-08-05T02:34:05.564Z vite:import-analysis 1.69ms [3 imports rewritten] node_modules/.vite/deps/react-router-dom.js?v=5981b4ff
2025-08-05T02:34:05.564Z vite:transform 2.15ms /node_modules/.vite/deps/react-router-dom.js?v=5981b4ff
2025-08-05T02:34:05.565Z vite:load 15.02ms [fs] /src/components/AppInitializer.tsx
2025-08-05T02:34:05.580Z vite:load 30.05ms [fs] /src/App.tsx
2025-08-05T02:34:05.592Z vite:load 42.04ms [fs] /src/styles/index.css
2025-08-05T02:34:05.592Z vite:hmr [self-accepts] src/styles/index.css
2025-08-05T02:34:05.593Z vite:import-analysis 0.27ms [0 imports rewritten] src/styles/index.css
2025-08-05T02:34:05.593Z vite:transform 0.63ms /src/styles/index.css
2025-08-05T02:34:05.595Z vite:load 37.95ms [plugin] /node_modules/.vite/deps/react_jsx-dev-runtime.js?v=5981b4ff
2025-08-05T02:34:05.595Z vite:import-analysis 0.42ms [2 imports rewritten] node_modules/.vite/deps/react_jsx-dev-runtime.js?v=5981b4ff
2025-08-05T02:34:05.596Z vite:transform 0.76ms /node_modules/.vite/deps/react_jsx-dev-runtime.js?v=5981b4ff
2025-08-05T02:34:05.599Z vite:resolve 1.59ms ./slices/authSlice -> /root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/web/frontend/src/store/slices/authSlice.ts
2025-08-05T02:34:05.599Z vite:resolve 1.61ms ./slices/gameSlice -> /root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/web/frontend/src/store/slices/gameSlice.ts
2025-08-05T02:34:05.599Z vite:resolve 1.63ms ./slices/uiSlice -> /root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/web/frontend/src/store/slices/uiSlice.ts
2025-08-05T02:34:05.599Z vite:resolve 1.64ms ./api/apiSlice -> /root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/web/frontend/src/store/api/apiSlice.ts
2025-08-05T02:34:05.599Z vite:resolve 1.64ms ./hooks -> /root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/web/frontend/src/store/hooks.ts
2025-08-05T02:34:05.599Z vite:cache [memory]  react/jsx-dev-runtime
2025-08-05T02:34:05.599Z vite:resolve 1.60ms ../services/authService -> /root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/web/frontend/src/services/authService.ts
2025-08-05T02:34:05.599Z vite:resolve 1.35ms ./pages/LoginPage -> /root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/web/frontend/src/pages/LoginPage.tsx
2025-08-05T02:34:05.599Z vite:resolve 1.36ms ./pages/AuthCallbackPage -> /root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/web/frontend/src/pages/AuthCallbackPage.tsx
2025-08-05T02:34:05.599Z vite:resolve 1.37ms ./pages/GameLobbyPage -> /root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/web/frontend/src/pages/GameLobbyPage.tsx
2025-08-05T02:34:05.599Z vite:resolve 1.38ms ./pages/WorldCreatePage -> /root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/web/frontend/src/pages/WorldCreatePage.tsx
2025-08-05T02:34:05.599Z vite:resolve 1.39ms ./pages/GamePage -> /root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/web/frontend/src/pages/GamePage.tsx
2025-08-05T02:34:05.599Z vite:resolve 1.41ms ./pages/ProfilePage -> /root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/web/frontend/src/pages/ProfilePage.tsx
2025-08-05T02:34:05.599Z vite:resolve 1.42ms ./components/layout/AppHeader -> /root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/web/frontend/src/components/layout/AppHeader.tsx
2025-08-05T02:34:05.599Z vite:resolve 1.43ms ./components/layout/AppFooter -> /root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/web/frontend/src/components/layout/AppFooter.tsx
2025-08-05T02:34:05.599Z vite:resolve 1.43ms ./components/DevModeIndicator -> /root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/web/frontend/src/components/DevModeIndicator.tsx
2025-08-05T02:34:05.599Z vite:resolve 1.85ms @reduxjs/toolkit -> /root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/web/frontend/node_modules/.vite/deps/@reduxjs_toolkit.js?v=5981b4ff
2025-08-05T02:34:05.599Z vite:resolve 1.86ms @reduxjs/toolkit/query -> /root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/web/frontend/node_modules/.vite/deps/@reduxjs_toolkit_query.js?v=5981b4ff
2025-08-05T02:34:05.599Z vite:resolve 1.80ms styled-components -> /root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/web/frontend/node_modules/.vite/deps/styled-components.js?v=5981b4ff
2025-08-05T02:34:05.599Z vite:resolve 1.56ms framer-motion -> /root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/web/frontend/node_modules/.vite/deps/framer-motion.js?v=5981b4ff
2025-08-05T02:34:05.600Z vite:load 0.31ms [plugin] /@react-refresh
2025-08-05T02:34:05.600Z vite:import-analysis /node_modules/.vite/deps/react.js?v=5981b4ff needs interop
2025-08-05T02:34:05.600Z vite:cache [memory] /node_modules/.vite/deps/react.js?v=5981b4ff
2025-08-05T02:34:05.600Z vite:cache [memory] /node_modules/.vite/deps/react-router-dom.js?v=5981b4ff
2025-08-05T02:34:05.600Z vite:optimize-deps load /root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/web/frontend/node_modules/.vite/deps/@reduxjs_toolkit.js
2025-08-05T02:34:05.600Z vite:optimize-deps load /root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/web/frontend/node_modules/.vite/deps/@reduxjs_toolkit_query.js
2025-08-05T02:34:05.600Z vite:optimize-deps load /root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/web/frontend/node_modules/.vite/deps/styled-components.js
2025-08-05T02:34:05.600Z vite:hmr [self-accepts] src/components/AppInitializer.tsx
2025-08-05T02:34:05.600Z vite:optimize-deps load /root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/web/frontend/node_modules/.vite/deps/framer-motion.js
2025-08-05T02:34:05.600Z vite:hmr [self-accepts] src/App.tsx
2025-08-05T02:34:05.600Z vite:import-analysis 3.11ms [7 imports rewritten] src/store/index.ts
2025-08-05T02:34:05.600Z vite:import-analysis 3.04ms [6 imports rewritten] src/components/AppInitializer.tsx
2025-08-05T02:34:05.600Z vite:import-analysis 3.05ms [15 imports rewritten] src/App.tsx
2025-08-05T02:34:05.600Z vite:transform 38.66ms /src/store/index.ts
2025-08-05T02:34:05.602Z vite:transform 37.08ms /src/components/AppInitializer.tsx
2025-08-05T02:34:05.602Z vite:transform 22.08ms /src/App.tsx
2025-08-05T02:34:05.603Z vite:import-analysis 0.15ms [no imports] /@react-refresh
2025-08-05T02:34:05.603Z vite:transform 3.04ms /@react-refresh
2025-08-05T02:34:05.606Z vite:load 46.15ms [plugin] /node_modules/.vite/deps/chunk-LXGCQ6UQ.js?v=5981b4ff
2025-08-05T02:34:05.606Z vite:import-analysis 0.66ms [1 imports rewritten] node_modules/.vite/deps/chunk-LXGCQ6UQ.js?v=5981b4ff
2025-08-05T02:34:05.606Z vite:transform 0.91ms /node_modules/.vite/deps/chunk-LXGCQ6UQ.js?v=5981b4ff
2025-08-05T02:34:05.607Z vite:load 47.20ms [plugin] /node_modules/.vite/deps/chunk-ROME4SDB.js?v=5981b4ff
2025-08-05T02:34:05.607Z vite:import-analysis 0.02ms [no imports] node_modules/.vite/deps/chunk-ROME4SDB.js?v=5981b4ff
2025-08-05T02:34:05.607Z vite:transform 0.14ms /node_modules/.vite/deps/chunk-ROME4SDB.js?v=5981b4ff
2025-08-05T02:34:05.607Z vite:load 46.01ms [plugin] /node_modules/.vite/deps/chunk-J2UTGV2I.js?v=5981b4ff
2025-08-05T02:34:05.607Z vite:cache [memory] /node_modules/.vite/deps/chunk-LXGCQ6UQ.js?v=5981b4ff
2025-08-05T02:34:05.607Z vite:cache [memory] /node_modules/.vite/deps/chunk-ROME4SDB.js?v=5981b4ff
2025-08-05T02:34:05.608Z vite:import-analysis 0.62ms [5 imports rewritten] node_modules/.vite/deps/chunk-J2UTGV2I.js?v=5981b4ff
2025-08-05T02:34:05.608Z vite:transform 0.76ms /node_modules/.vite/deps/chunk-J2UTGV2I.js?v=5981b4ff
2025-08-05T02:34:05.608Z vite:load 46.83ms [plugin] /node_modules/.vite/deps/chunk-HJ4OHFNV.js?v=5981b4ff
2025-08-05T02:34:05.608Z vite:cache [memory] /node_modules/.vite/deps/chunk-ROME4SDB.js?v=5981b4ff
2025-08-05T02:34:05.608Z vite:import-analysis 0.15ms [1 imports rewritten] node_modules/.vite/deps/chunk-HJ4OHFNV.js?v=5981b4ff
2025-08-05T02:34:05.608Z vite:transform 0.22ms /node_modules/.vite/deps/chunk-HJ4OHFNV.js?v=5981b4ff
2025-08-05T02:34:05.609Z vite:load 47.99ms [plugin] /node_modules/.vite/deps/chunk-SNXB62YR.js?v=5981b4ff
2025-08-05T02:34:05.609Z vite:import-analysis 0.01ms [no imports] node_modules/.vite/deps/chunk-SNXB62YR.js?v=5981b4ff
2025-08-05T02:34:05.609Z vite:transform 0.07ms /node_modules/.vite/deps/chunk-SNXB62YR.js?v=5981b4ff
2025-08-05T02:34:05.611Z vite:load 10.74ms [plugin] /node_modules/.vite/deps/@reduxjs_toolkit.js?v=5981b4ff
2025-08-05T02:34:05.611Z vite:resolve 0.07ms ./chunk-GBXKZDP3.js -> /root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/web/frontend/node_modules/.vite/deps/chunk-GBXKZDP3.js?v=5981b4ff
2025-08-05T02:34:05.611Z vite:resolve 0.09ms ./chunk-5F26ILMS.js -> /root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/web/frontend/node_modules/.vite/deps/chunk-5F26ILMS.js?v=5981b4ff
2025-08-05T02:34:05.611Z vite:cache [memory] /node_modules/.vite/deps/chunk-ROME4SDB.js?v=5981b4ff
2025-08-05T02:34:05.611Z vite:optimize-deps load /root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/web/frontend/node_modules/.vite/deps/chunk-GBXKZDP3.js
2025-08-05T02:34:05.611Z vite:optimize-deps load /root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/web/frontend/node_modules/.vite/deps/chunk-5F26ILMS.js
2025-08-05T02:34:05.611Z vite:import-analysis 0.36ms [3 imports rewritten] node_modules/.vite/deps/@reduxjs_toolkit.js?v=5981b4ff
2025-08-05T02:34:05.611Z vite:transform 0.42ms /node_modules/.vite/deps/@reduxjs_toolkit.js?v=5981b4ff
2025-08-05T02:34:05.611Z vite:load 11.21ms [plugin] /node_modules/.vite/deps/@reduxjs_toolkit_query.js?v=5981b4ff
2025-08-05T02:34:05.611Z vite:resolve 0.07ms ./chunk-4UNUQYEM.js -> /root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/web/frontend/node_modules/.vite/deps/chunk-4UNUQYEM.js?v=5981b4ff
2025-08-05T02:34:05.611Z vite:cache [memory] /node_modules/.vite/deps/chunk-ROME4SDB.js?v=5981b4ff
2025-08-05T02:34:05.611Z vite:optimize-deps load /root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/web/frontend/node_modules/.vite/deps/chunk-4UNUQYEM.js
2025-08-05T02:34:05.612Z vite:import-analysis 0.27ms [4 imports rewritten] node_modules/.vite/deps/@reduxjs_toolkit_query.js?v=5981b4ff
2025-08-05T02:34:05.612Z vite:transform 0.33ms /node_modules/.vite/deps/@reduxjs_toolkit_query.js?v=5981b4ff
2025-08-05T02:34:05.612Z vite:load 11.46ms [plugin] /node_modules/.vite/deps/styled-components.js?v=5981b4ff
2025-08-05T02:34:05.612Z vite:resolve 0.07ms ./chunk-JR5FH6E4.js -> /root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/web/frontend/node_modules/.vite/deps/chunk-JR5FH6E4.js?v=5981b4ff
2025-08-05T02:34:05.612Z vite:cache [memory] /node_modules/.vite/deps/chunk-HJ4OHFNV.js?v=5981b4ff
2025-08-05T02:34:05.612Z vite:cache [memory] /node_modules/.vite/deps/chunk-LXGCQ6UQ.js?v=5981b4ff
2025-08-05T02:34:05.612Z vite:cache [memory] /node_modules/.vite/deps/chunk-ROME4SDB.js?v=5981b4ff
2025-08-05T02:34:05.612Z vite:optimize-deps load /root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/web/frontend/node_modules/.vite/deps/chunk-JR5FH6E4.js
2025-08-05T02:34:05.612Z vite:import-analysis 0.75ms [4 imports rewritten] node_modules/.vite/deps/styled-components.js?v=5981b4ff
2025-08-05T02:34:05.613Z vite:transform 0.93ms /node_modules/.vite/deps/styled-components.js?v=5981b4ff
2025-08-05T02:34:05.613Z vite:load 52.61ms [plugin] /node_modules/.vite/deps/chunk-ZRNALROW.js?v=5981b4ff
2025-08-05T02:34:05.621Z vite:cache [memory] /node_modules/.vite/deps/chunk-LXGCQ6UQ.js?v=5981b4ff
2025-08-05T02:34:05.621Z vite:cache [memory] /node_modules/.vite/deps/chunk-ROME4SDB.js?v=5981b4ff
2025-08-05T02:34:05.621Z vite:import-analysis 6.14ms [2 imports rewritten] node_modules/.vite/deps/chunk-ZRNALROW.js?v=5981b4ff
2025-08-05T02:34:05.621Z vite:transform 8.83ms /node_modules/.vite/deps/chunk-ZRNALROW.js?v=5981b4ff
2025-08-05T02:34:05.624Z vite:load 24.57ms [fs] /src/store/slices/authSlice.ts
2025-08-05T02:34:05.624Z vite:load 24.92ms [fs] /src/store/slices/gameSlice.ts
2025-08-05T02:34:05.625Z vite:load 25.12ms [fs] /src/store/slices/uiSlice.ts
2025-08-05T02:34:05.625Z vite:load 24.72ms [plugin] /node_modules/.vite/deps/framer-motion.js?v=5981b4ff
2025-08-05T02:34:05.628Z vite:cache [memory] /node_modules/.vite/deps/chunk-LXGCQ6UQ.js?v=5981b4ff
2025-08-05T02:34:05.628Z vite:cache [memory] /node_modules/.vite/deps/chunk-ROME4SDB.js?v=5981b4ff
2025-08-05T02:34:05.628Z vite:import-analysis 2.38ms [2 imports rewritten] node_modules/.vite/deps/framer-motion.js?v=5981b4ff
2025-08-05T02:34:05.628Z vite:transform 3.00ms /node_modules/.vite/deps/framer-motion.js?v=5981b4ff
2025-08-05T02:34:05.628Z vite:load 28.92ms [fs] /src/store/api/apiSlice.ts
2025-08-05T02:34:05.629Z vite:load 29.28ms [fs] /src/store/hooks.ts
2025-08-05T02:34:05.629Z vite:load 29.04ms [fs] /src/services/authService.ts
2025-08-05T02:34:05.629Z vite:load 29.27ms [fs] /src/pages/LoginPage.tsx
2025-08-05T02:34:05.643Z vite:load 43.23ms [fs] /src/pages/AuthCallbackPage.tsx
2025-08-05T02:34:05.657Z vite:load 56.75ms [fs] /src/pages/GameLobbyPage.tsx
2025-08-05T02:34:05.671Z vite:load 71.55ms [fs] /src/pages/WorldCreatePage.tsx
2025-08-05T02:34:05.689Z vite:load 88.83ms [fs] /src/pages/GamePage.tsx
2025-08-05T02:34:05.705Z vite:load 105.03ms [fs] /src/pages/ProfilePage.tsx
2025-08-05T02:34:05.719Z vite:load 119.48ms [fs] /src/components/layout/AppHeader.tsx
2025-08-05T02:34:05.725Z vite:load 125.11ms [fs] /src/components/layout/AppFooter.tsx
2025-08-05T02:34:05.727Z vite:load 127.48ms [fs] /src/components/DevModeIndicator.tsx
2025-08-05T02:34:05.736Z vite:resolve 0.31ms ../store -> /root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/web/frontend/src/store/index.ts
2025-08-05T02:34:05.736Z vite:resolve 0.36ms ../store/slices/authSlice -> /root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/web/frontend/src/store/slices/authSlice.ts
2025-08-05T02:34:05.736Z vite:resolve 0.51ms @reduxjs/toolkit/query/react -> /root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/web/frontend/node_modules/.vite/deps/@reduxjs_toolkit_query_react.js?v=5981b4ff
2025-08-05T02:34:05.736Z vite:cache [memory] /src/store/index.ts
2025-08-05T02:34:05.736Z vite:cache [memory] /node_modules/.vite/deps/@reduxjs_toolkit.js?v=5981b4ff
2025-08-05T02:34:05.736Z vite:cache [memory] /node_modules/.vite/deps/react-redux.js?v=5981b4ff
2025-08-05T02:34:05.736Z vite:optimize-deps load /root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/web/frontend/node_modules/.vite/deps/@reduxjs_toolkit_query_react.js
2025-08-05T02:34:05.736Z vite:import-analysis 1.15ms [2 imports rewritten] src/services/authService.ts
2025-08-05T02:34:05.736Z vite:import-analysis 1.20ms [1 imports rewritten] src/store/slices/authSlice.ts
2025-08-05T02:34:05.736Z vite:import-analysis 1.20ms [1 imports rewritten] src/store/slices/gameSlice.ts
2025-08-05T02:34:05.736Z vite:import-analysis 1.20ms [1 imports rewritten] src/store/slices/uiSlice.ts
2025-08-05T02:34:05.736Z vite:import-analysis 1.21ms [1 imports rewritten] src/store/hooks.ts
2025-08-05T02:34:05.736Z vite:import-analysis 1.22ms [1 imports rewritten] src/store/api/apiSlice.ts
2025-08-05T02:34:05.736Z vite:transform 107.47ms /src/services/authService.ts
2025-08-05T02:34:05.737Z vite:transform 112.48ms /src/store/slices/authSlice.ts
2025-08-05T02:34:05.737Z vite:transform 112.18ms /src/store/slices/gameSlice.ts
2025-08-05T02:34:05.737Z vite:transform 112.00ms /src/store/slices/uiSlice.ts
2025-08-05T02:34:05.737Z vite:transform 107.92ms /src/store/hooks.ts
2025-08-05T02:34:05.737Z vite:transform 108.31ms /src/store/api/apiSlice.ts
2025-08-05T02:34:05.738Z vite:cache [memory] /@react-refresh
2025-08-05T02:34:05.738Z vite:cache [memory]  react/jsx-dev-runtime
2025-08-05T02:34:05.738Z vite:resolve 0.90ms ../store/hooks -> /root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/web/frontend/src/store/hooks.ts
2025-08-05T02:34:05.738Z vite:resolve 0.93ms ../store/slices/uiSlice -> /root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/web/frontend/src/store/slices/uiSlice.ts
2025-08-05T02:34:05.738Z vite:resolve 0.97ms @ant-design/icons -> /root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/web/frontend/node_modules/.vite/deps/@ant-design_icons.js?v=5981b4ff
2025-08-05T02:34:05.739Z vite:import-analysis /node_modules/.vite/deps/react.js?v=5981b4ff needs interop
2025-08-05T02:34:05.739Z vite:import-analysis /node_modules/.vite/deps/react.js?v=5981b4ff needs interop
2025-08-05T02:34:05.739Z vite:cache [memory] /node_modules/.vite/deps/react.js?v=5981b4ff
2025-08-05T02:34:05.739Z vite:cache [memory] /node_modules/.vite/deps/react-router-dom.js?v=5981b4ff
2025-08-05T02:34:05.739Z vite:cache [memory] /node_modules/.vite/deps/styled-components.js?v=5981b4ff
2025-08-05T02:34:05.739Z vite:cache [memory] /node_modules/.vite/deps/framer-motion.js?v=5981b4ff
2025-08-05T02:34:05.739Z vite:optimize-deps load /root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/web/frontend/node_modules/.vite/deps/@ant-design_icons.js
2025-08-05T02:34:05.739Z vite:hmr [self-accepts] src/pages/LoginPage.tsx
2025-08-05T02:34:05.739Z vite:hmr [self-accepts] src/pages/AuthCallbackPage.tsx
2025-08-05T02:34:05.739Z vite:import-analysis 1.98ms [12 imports rewritten] src/pages/LoginPage.tsx
2025-08-05T02:34:05.739Z vite:import-analysis 1.99ms [11 imports rewritten] src/pages/AuthCallbackPage.tsx
2025-08-05T02:34:05.741Z vite:transform 111.76ms /src/pages/LoginPage.tsx
2025-08-05T02:34:05.741Z vite:transform 97.84ms /src/pages/AuthCallbackPage.tsx
2025-08-05T02:34:05.743Z vite:cache [memory] /@react-refresh
2025-08-05T02:34:05.743Z vite:cache [memory]  react/jsx-dev-runtime
2025-08-05T02:34:05.743Z vite:resolve 1.10ms ../store/slices/gameSlice -> /root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/web/frontend/src/store/slices/gameSlice.ts
2025-08-05T02:34:05.743Z vite:resolve 1.11ms ../store/api -> /root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/web/frontend/src/store/api/index.ts
2025-08-05T02:34:05.743Z vite:resolve 1.12ms ../components/WorldDetailModal -> /root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/web/frontend/src/components/WorldDetailModal.tsx
2025-08-05T02:34:05.743Z vite:import-analysis /node_modules/.vite/deps/react.js?v=5981b4ff needs interop
2025-08-05T02:34:05.743Z vite:import-analysis /node_modules/.vite/deps/react.js?v=5981b4ff needs interop
2025-08-05T02:34:05.744Z vite:cache [memory] /node_modules/.vite/deps/react.js?v=5981b4ff
2025-08-05T02:34:05.744Z vite:cache [memory] /node_modules/.vite/deps/react-router-dom.js?v=5981b4ff
2025-08-05T02:34:05.744Z vite:cache [memory] /node_modules/.vite/deps/styled-components.js?v=5981b4ff
2025-08-05T02:34:05.744Z vite:cache [memory] /node_modules/.vite/deps/framer-motion.js?v=5981b4ff
2025-08-05T02:34:05.744Z vite:hmr [self-accepts] src/pages/GameLobbyPage.tsx
2025-08-05T02:34:05.744Z vite:hmr [self-accepts] src/pages/WorldCreatePage.tsx
2025-08-05T02:34:05.744Z vite:import-analysis 2.09ms [13 imports rewritten] src/pages/GameLobbyPage.tsx
2025-08-05T02:34:05.744Z vite:import-analysis 2.11ms [10 imports rewritten] src/pages/WorldCreatePage.tsx
2025-08-05T02:34:05.747Z vite:transform 90.24ms /src/pages/GameLobbyPage.tsx
2025-08-05T02:34:05.747Z vite:transform 75.54ms /src/pages/WorldCreatePage.tsx
2025-08-05T02:34:05.752Z vite:cache [memory] /@react-refresh
2025-08-05T02:34:05.752Z vite:cache [memory]  react/jsx-dev-runtime
2025-08-05T02:34:05.752Z vite:resolve 0.57ms ../components/CharacterSelectionModal -> /root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/web/frontend/src/components/CharacterSelectionModal.tsx
2025-08-05T02:34:05.752Z vite:import-analysis /node_modules/.vite/deps/react.js?v=5981b4ff needs interop
2025-08-05T02:34:05.753Z vite:cache [memory] /node_modules/.vite/deps/react.js?v=5981b4ff
2025-08-05T02:34:05.753Z vite:cache [memory] /node_modules/.vite/deps/react-router-dom.js?v=5981b4ff
2025-08-05T02:34:05.753Z vite:cache [memory] /node_modules/.vite/deps/styled-components.js?v=5981b4ff
2025-08-05T02:34:05.753Z vite:hmr [self-accepts] src/pages/GamePage.tsx
2025-08-05T02:34:05.753Z vite:import-analysis 1.29ms [11 imports rewritten] src/pages/GamePage.tsx
2025-08-05T02:34:05.753Z vite:transform 64.50ms /src/pages/GamePage.tsx
2025-08-05T02:34:05.755Z vite:cache [memory] /@react-refresh
2025-08-05T02:34:05.755Z vite:cache [memory]  react/jsx-dev-runtime
2025-08-05T02:34:05.755Z vite:resolve 0.50ms ../services -> /root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/web/frontend/src/services/index.ts
2025-08-05T02:34:05.755Z vite:import-analysis /node_modules/.vite/deps/react.js?v=5981b4ff needs interop
2025-08-05T02:34:05.755Z vite:cache [memory] /node_modules/.vite/deps/react.js?v=5981b4ff
2025-08-05T02:34:05.755Z vite:cache [memory] /node_modules/.vite/deps/styled-components.js?v=5981b4ff
2025-08-05T02:34:05.755Z vite:cache [memory] /node_modules/.vite/deps/react-router-dom.js?v=5981b4ff
2025-08-05T02:34:05.755Z vite:hmr [self-accepts] src/pages/ProfilePage.tsx
2025-08-05T02:34:05.755Z vite:import-analysis 1.12ms [11 imports rewritten] src/pages/ProfilePage.tsx
2025-08-05T02:34:05.756Z vite:transform 50.40ms /src/pages/ProfilePage.tsx
2025-08-05T02:34:05.757Z vite:cache [memory] /@react-refresh
2025-08-05T02:34:05.757Z vite:cache [memory]  react/jsx-dev-runtime
2025-08-05T02:34:05.757Z vite:resolve 0.84ms ../../store/hooks -> /root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/web/frontend/src/store/hooks.ts
2025-08-05T02:34:05.757Z vite:resolve 0.85ms ../../store/slices/authSlice -> /root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/web/frontend/src/store/slices/authSlice.ts
2025-08-05T02:34:05.757Z vite:resolve 0.86ms ../../store/slices/uiSlice -> /root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/web/frontend/src/store/slices/uiSlice.ts
2025-08-05T02:34:05.757Z vite:import-analysis /node_modules/.vite/deps/react.js?v=5981b4ff needs interop
2025-08-05T02:34:05.761Z vite:cache [memory] /node_modules/.vite/deps/react-router-dom.js?v=5981b4ff
2025-08-05T02:34:05.761Z vite:cache [memory] /node_modules/.vite/deps/styled-components.js?v=5981b4ff
2025-08-05T02:34:05.761Z vite:cache [memory] /node_modules/.vite/deps/react.js?v=5981b4ff
2025-08-05T02:34:05.761Z vite:hmr [self-accepts] src/components/layout/AppHeader.tsx
2025-08-05T02:34:05.761Z vite:hmr [self-accepts] src/components/layout/AppFooter.tsx
2025-08-05T02:34:05.761Z vite:hmr [self-accepts] src/components/DevModeIndicator.tsx
2025-08-05T02:34:05.761Z vite:import-analysis 4.57ms [9 imports rewritten] src/components/layout/AppHeader.tsx
2025-08-05T02:34:05.761Z vite:import-analysis 4.58ms [5 imports rewritten] src/components/layout/AppFooter.tsx
2025-08-05T02:34:05.761Z vite:import-analysis 4.59ms [9 imports rewritten] src/components/DevModeIndicator.tsx
2025-08-05T02:34:05.761Z vite:transform 41.84ms /src/components/layout/AppHeader.tsx
2025-08-05T02:34:05.761Z vite:transform 36.36ms /src/components/layout/AppFooter.tsx
2025-08-05T02:34:05.761Z vite:transform 34.01ms /src/components/DevModeIndicator.tsx
2025-08-05T02:34:05.762Z vite:load 208.83ms [plugin] /node_modules/.vite/deps/antd.js?v=5981b4ff
2025-08-05T02:34:05.795Z vite:resolve 0.16ms ./chunk-WPFDAWNF.js -> /root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/web/frontend/node_modules/.vite/deps/chunk-WPFDAWNF.js?v=5981b4ff
2025-08-05T02:34:05.795Z vite:cache [memory] /node_modules/.vite/deps/chunk-ZRNALROW.js?v=5981b4ff
2025-08-05T02:34:05.795Z vite:cache [memory] /node_modules/.vite/deps/chunk-SNXB62YR.js?v=5981b4ff
2025-08-05T02:34:05.795Z vite:cache [memory] /node_modules/.vite/deps/chunk-LXGCQ6UQ.js?v=5981b4ff
2025-08-05T02:34:05.795Z vite:cache [memory] /node_modules/.vite/deps/chunk-ROME4SDB.js?v=5981b4ff
2025-08-05T02:34:05.795Z vite:optimize-deps load /root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/web/frontend/node_modules/.vite/deps/chunk-WPFDAWNF.js
2025-08-05T02:34:05.795Z vite:import-analysis 24.88ms [7 imports rewritten] node_modules/.vite/deps/antd.js?v=5981b4ff
2025-08-05T02:34:05.795Z vite:transform 33.90ms /node_modules/.vite/deps/antd.js?v=5981b4ff
2025-08-05T02:34:05.806Z vite:load 194.56ms [plugin] /node_modules/.vite/deps/chunk-5F26ILMS.js?v=5981b4ff
2025-08-05T02:34:05.806Z vite:import-analysis 0.03ms [no imports] node_modules/.vite/deps/chunk-5F26ILMS.js?v=5981b4ff
2025-08-05T02:34:05.806Z vite:transform 0.14ms /node_modules/.vite/deps/chunk-5F26ILMS.js?v=5981b4ff
2025-08-05T02:34:05.806Z vite:load 194.80ms [plugin] /node_modules/.vite/deps/chunk-GBXKZDP3.js?v=5981b4ff
2025-08-05T02:34:05.807Z vite:cache [memory] /node_modules/.vite/deps/chunk-5F26ILMS.js?v=5981b4ff
2025-08-05T02:34:05.807Z vite:import-analysis 0.90ms [1 imports rewritten] node_modules/.vite/deps/chunk-GBXKZDP3.js?v=5981b4ff
2025-08-05T02:34:05.807Z vite:transform 1.15ms /node_modules/.vite/deps/chunk-GBXKZDP3.js?v=5981b4ff
2025-08-05T02:34:05.807Z vite:load 194.79ms [plugin] /node_modules/.vite/deps/chunk-JR5FH6E4.js?v=5981b4ff
2025-08-05T02:34:05.807Z vite:import-analysis 0.01ms [no imports] node_modules/.vite/deps/chunk-JR5FH6E4.js?v=5981b4ff
2025-08-05T02:34:05.807Z vite:transform 0.07ms /node_modules/.vite/deps/chunk-JR5FH6E4.js?v=5981b4ff
2025-08-05T02:34:05.807Z vite:load 195.81ms [plugin] /node_modules/.vite/deps/chunk-4UNUQYEM.js?v=5981b4ff
2025-08-05T02:34:05.808Z vite:cache [memory] /node_modules/.vite/deps/chunk-GBXKZDP3.js?v=5981b4ff
2025-08-05T02:34:05.808Z vite:import-analysis 0.64ms [1 imports rewritten] node_modules/.vite/deps/chunk-4UNUQYEM.js?v=5981b4ff
2025-08-05T02:34:05.808Z vite:transform 0.82ms /node_modules/.vite/deps/chunk-4UNUQYEM.js?v=5981b4ff
2025-08-05T02:34:05.809Z vite:load 72.26ms [plugin] /node_modules/.vite/deps/@reduxjs_toolkit_query_react.js?v=5981b4ff
2025-08-05T02:34:05.809Z vite:cache [memory] /node_modules/.vite/deps/chunk-J2UTGV2I.js?v=5981b4ff
2025-08-05T02:34:05.809Z vite:cache [memory] /node_modules/.vite/deps/chunk-ZRNALROW.js?v=5981b4ff
2025-08-05T02:34:05.809Z vite:cache [memory] /node_modules/.vite/deps/chunk-HJ4OHFNV.js?v=5981b4ff
2025-08-05T02:34:05.809Z vite:cache [memory] /node_modules/.vite/deps/chunk-SNXB62YR.js?v=5981b4ff
2025-08-05T02:34:05.809Z vite:cache [memory] /node_modules/.vite/deps/chunk-LXGCQ6UQ.js?v=5981b4ff
2025-08-05T02:34:05.809Z vite:cache [memory] /node_modules/.vite/deps/chunk-4UNUQYEM.js?v=5981b4ff
2025-08-05T02:34:05.809Z vite:cache [memory] /node_modules/.vite/deps/chunk-GBXKZDP3.js?v=5981b4ff
2025-08-05T02:34:05.809Z vite:cache [memory] /node_modules/.vite/deps/chunk-5F26ILMS.js?v=5981b4ff
2025-08-05T02:34:05.809Z vite:cache [memory] /node_modules/.vite/deps/chunk-ROME4SDB.js?v=5981b4ff
2025-08-05T02:34:05.809Z vite:import-analysis 0.58ms [9 imports rewritten] node_modules/.vite/deps/@reduxjs_toolkit_query_react.js?v=5981b4ff
2025-08-05T02:34:05.809Z vite:transform 0.67ms /node_modules/.vite/deps/@reduxjs_toolkit_query_react.js?v=5981b4ff
2025-08-05T02:34:05.809Z vite:load 65.88ms [fs] /src/store/api/index.ts
2025-08-05T02:34:05.810Z vite:load 70.49ms [plugin] /node_modules/.vite/deps/@ant-design_icons.js?v=5981b4ff
2025-08-05T02:34:05.811Z vite:cache [memory] /node_modules/.vite/deps/chunk-SNXB62YR.js?v=5981b4ff
2025-08-05T02:34:05.811Z vite:cache [memory] /node_modules/.vite/deps/chunk-LXGCQ6UQ.js?v=5981b4ff
2025-08-05T02:34:05.811Z vite:cache [memory] /node_modules/.vite/deps/chunk-5F26ILMS.js?v=5981b4ff
2025-08-05T02:34:05.811Z vite:cache [memory] /node_modules/.vite/deps/chunk-ROME4SDB.js?v=5981b4ff
2025-08-05T02:34:05.811Z vite:import-analysis 0.94ms [5 imports rewritten] node_modules/.vite/deps/@ant-design_icons.js?v=5981b4ff
2025-08-05T02:34:05.811Z vite:transform 1.09ms /node_modules/.vite/deps/@ant-design_icons.js?v=5981b4ff
2025-08-05T02:34:05.811Z vite:load 67.31ms [fs] /src/components/WorldDetailModal.tsx
2025-08-05T02:34:05.818Z vite:load 65.81ms [fs] /src/components/CharacterSelectionModal.tsx
2025-08-05T02:34:05.824Z vite:load 68.93ms [fs] /src/services/index.ts
2025-08-05T02:34:05.826Z vite:resolve 0.77ms ./apiSlice -> /root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/web/frontend/src/store/api/apiSlice.ts
2025-08-05T02:34:05.826Z vite:resolve 0.82ms ./authApi -> /root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/web/frontend/src/store/api/authApi.ts
2025-08-05T02:34:05.826Z vite:resolve 0.86ms ./worldApi -> /root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/web/frontend/src/store/api/worldApi.ts
2025-08-05T02:34:05.826Z vite:resolve 0.89ms ./characterApi -> /root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/web/frontend/src/store/api/characterApi.ts
2025-08-05T02:34:05.826Z vite:resolve 0.91ms ./gameInteractionApi -> /root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/web/frontend/src/store/api/gameInteractionApi.ts
2025-08-05T02:34:05.826Z vite:resolve 0.93ms ./aiApi -> /root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/web/frontend/src/store/api/aiApi.ts
2025-08-05T02:34:05.826Z vite:cache [memory] /@react-refresh
2025-08-05T02:34:05.826Z vite:cache [memory]  react/jsx-dev-runtime
2025-08-05T02:34:05.826Z vite:cache [memory] /src/store/api/apiSlice.ts
2025-08-05T02:34:05.826Z vite:import-analysis /node_modules/.vite/deps/react.js?v=5981b4ff needs interop
2025-08-05T02:34:05.826Z vite:cache [memory] /node_modules/.vite/deps/react.js?v=5981b4ff
2025-08-05T02:34:05.826Z vite:cache [memory] /node_modules/.vite/deps/antd.js?v=5981b4ff
2025-08-05T02:34:05.826Z vite:cache [memory] /node_modules/.vite/deps/@ant-design_icons.js?v=5981b4ff
2025-08-05T02:34:05.826Z vite:cache [memory] /node_modules/.vite/deps/styled-components.js?v=5981b4ff
2025-08-05T02:34:05.826Z vite:hmr [self-accepts] src/components/WorldDetailModal.tsx
2025-08-05T02:34:05.826Z vite:import-analysis 1.68ms [6 imports rewritten] src/store/api/index.ts
2025-08-05T02:34:05.826Z vite:import-analysis 1.61ms [7 imports rewritten] src/components/WorldDetailModal.tsx
2025-08-05T02:34:05.826Z vite:transform 17.07ms /src/store/api/index.ts
2025-08-05T02:34:05.827Z vite:transform 15.93ms /src/components/WorldDetailModal.tsx
2025-08-05T02:34:05.831Z vite:cache [memory] /@react-refresh
2025-08-05T02:34:05.831Z vite:resolve 0.57ms ./authService -> /root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/web/frontend/src/services/authService.ts
2025-08-05T02:34:05.831Z vite:resolve 0.58ms ./gameService -> /root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/web/frontend/src/services/gameService.ts
2025-08-05T02:34:05.831Z vite:cache [memory]  react/jsx-dev-runtime
2025-08-05T02:34:05.831Z vite:cache [memory] /src/services/authService.ts
2025-08-05T02:34:05.831Z vite:cache [memory] /src/store/api/index.ts
2025-08-05T02:34:05.831Z vite:import-analysis /node_modules/.vite/deps/react.js?v=5981b4ff needs interop
2025-08-05T02:34:05.831Z vite:cache [memory] /node_modules/.vite/deps/react.js?v=5981b4ff
2025-08-05T02:34:05.831Z vite:cache [memory] /node_modules/.vite/deps/antd.js?v=5981b4ff
2025-08-05T02:34:05.831Z vite:cache [memory] /node_modules/.vite/deps/@ant-design_icons.js?v=5981b4ff
2025-08-05T02:34:05.831Z vite:cache [memory] /node_modules/.vite/deps/styled-components.js?v=5981b4ff
2025-08-05T02:34:05.831Z vite:hmr [self-accepts] src/components/CharacterSelectionModal.tsx
2025-08-05T02:34:05.831Z vite:import-analysis 1.08ms [2 imports rewritten] src/services/index.ts
2025-08-05T02:34:05.831Z vite:import-analysis 1.10ms [7 imports rewritten] src/components/CharacterSelectionModal.tsx
2025-08-05T02:34:05.831Z vite:transform 7.19ms /src/services/index.ts
2025-08-05T02:34:05.831Z vite:transform 12.97ms /src/components/CharacterSelectionModal.tsx
2025-08-05T02:34:05.831Z vite:load 36.09ms [plugin] /node_modules/.vite/deps/chunk-WPFDAWNF.js?v=5981b4ff
2025-08-05T02:34:05.843Z vite:cache [memory] /node_modules/.vite/deps/chunk-SNXB62YR.js?v=5981b4ff
2025-08-05T02:34:05.843Z vite:cache [memory] /node_modules/.vite/deps/chunk-LXGCQ6UQ.js?v=5981b4ff
2025-08-05T02:34:05.843Z vite:cache [memory] /node_modules/.vite/deps/chunk-5F26ILMS.js?v=5981b4ff
2025-08-05T02:34:05.843Z vite:cache [memory] /node_modules/.vite/deps/chunk-ROME4SDB.js?v=5981b4ff
2025-08-05T02:34:05.843Z vite:import-analysis 6.22ms [4 imports rewritten] node_modules/.vite/deps/chunk-WPFDAWNF.js?v=5981b4ff
2025-08-05T02:34:05.843Z vite:transform 11.38ms /node_modules/.vite/deps/chunk-WPFDAWNF.js?v=5981b4ff
2025-08-05T02:34:05.845Z vite:load 18.92ms [fs] /src/store/api/authApi.ts
2025-08-05T02:34:05.845Z vite:load 19.17ms [fs] /src/store/api/worldApi.ts
2025-08-05T02:34:05.845Z vite:load 19.32ms [fs] /src/store/api/characterApi.ts
2025-08-05T02:34:05.845Z vite:load 19.47ms [fs] /src/store/api/gameInteractionApi.ts
2025-08-05T02:34:05.846Z vite:load 19.60ms [fs] /src/store/api/aiApi.ts
2025-08-05T02:34:05.846Z vite:load 15.02ms [fs] /src/services/gameService.ts
2025-08-05T02:34:05.847Z vite:cache [memory] /src/store/api/apiSlice.ts
2025-08-05T02:34:05.847Z vite:import-analysis 0.30ms [1 imports rewritten] src/store/api/authApi.ts
2025-08-05T02:34:05.847Z vite:transform 2.26ms /src/store/api/authApi.ts
2025-08-05T02:34:05.848Z vite:cache [memory] /src/store/api/apiSlice.ts
2025-08-05T02:34:05.848Z vite:import-analysis 0.19ms [1 imports rewritten] src/store/api/characterApi.ts
2025-08-05T02:34:05.848Z vite:transform 2.49ms /src/store/api/characterApi.ts
2025-08-05T02:34:05.848Z vite:cache [memory] /src/store/api/apiSlice.ts
2025-08-05T02:34:05.848Z vite:import-analysis 0.30ms [1 imports rewritten] src/store/api/worldApi.ts
2025-08-05T02:34:05.848Z vite:import-analysis 0.33ms [1 imports rewritten] src/store/api/aiApi.ts
2025-08-05T02:34:05.848Z vite:transform 3.19ms /src/store/api/worldApi.ts
2025-08-05T02:34:05.848Z vite:transform 2.80ms /src/store/api/aiApi.ts
2025-08-05T02:34:05.849Z vite:cache [memory] /src/store/api/apiSlice.ts
2025-08-05T02:34:05.849Z vite:cache [memory] /src/store/index.ts
2025-08-05T02:34:05.849Z vite:cache [memory] /src/store/slices/gameSlice.ts
2025-08-05T02:34:05.849Z vite:import-analysis 0.38ms [1 imports rewritten] src/store/api/gameInteractionApi.ts
2025-08-05T02:34:05.849Z vite:import-analysis 0.39ms [2 imports rewritten] src/services/gameService.ts
2025-08-05T02:34:05.849Z vite:transform 3.57ms /src/store/api/gameInteractionApi.ts
2025-08-05T02:34:05.849Z vite:transform 3.21ms /src/services/gameService.ts
2025-08-05T02:35:40.723Z vite:html-fallback Rewriting GET / to /index.html
2025-08-05T02:35:40.727Z vite:cache [memory] /src/main.tsx
2025-08-05T02:35:40.728Z vite:import-analysis [skipped] index.html?html-proxy&direct&index=0.css
2025-08-05T02:35:40.728Z vite:time 4.79ms /index.html
2025-08-05T02:35:41.149Z vite:html-fallback Not rewriting GET /.well-known/appspecific/com.chrome.devtools.json because the client did not send an HTTP accept header.
2025-08-05T02:35:41.149Z vite:time 0.59ms /.well-known/appspecific/com.chrome.devtools.json
2025-08-05T02:35:41.477Z vite:cache [memory] /@react-refresh
2025-08-05T02:35:41.477Z vite:time 0.52ms /@react-refresh
2025-08-05T02:35:42.541Z vite:cache [memory] /src/main.tsx
2025-08-05T02:35:42.541Z vite:time 0.52ms /src/main.tsx
2025-08-05T02:35:42.542Z vite:resolve 0.17ms /@vite/client -> /root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/web/frontend/node_modules/vite/dist/client/client.mjs
2025-08-05T02:35:42.542Z vite:load 0.20ms [fs] /@vite/client
2025-08-05T02:35:42.543Z vite:resolve 0.05ms @vite/env -> /root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/web/frontend/node_modules/vite/dist/client/env.mjs
2025-08-05T02:35:42.543Z vite:import-analysis 0.40ms [1 imports rewritten] node_modules/vite/dist/client/client.mjs
2025-08-05T02:35:42.543Z vite:transform 0.70ms /@vite/client
2025-08-05T02:35:42.544Z vite:time 2.67ms /@vite/client
2025-08-05T02:35:42.544Z vite:load 1.00ms [fs] /node_modules/vite/dist/client/env.mjs
2025-08-05T02:35:42.545Z vite:import-analysis 0.02ms [no imports] node_modules/vite/dist/client/env.mjs
2025-08-05T02:35:42.545Z vite:transform 0.10ms /node_modules/vite/dist/client/env.mjs
2025-08-05T02:35:43.177Z vite:cache [memory] /node_modules/.vite/deps/react-dom_client.js?v=5981b4ff
2025-08-05T02:35:43.177Z vite:time 0.35ms /node_modules/.vite/deps/react-dom_client.js?v=5981b4ff
2025-08-05T02:35:43.177Z vite:cache [memory]  react/jsx-dev-runtime
2025-08-05T02:35:43.177Z vite:time 0.15ms /@id/__x00__react/jsx-dev-runtime
2025-08-05T02:35:43.178Z vite:cache [memory] /node_modules/.vite/deps/react.js?v=5981b4ff
2025-08-05T02:35:43.178Z vite:time 0.35ms /node_modules/.vite/deps/react.js?v=5981b4ff
2025-08-05T02:35:43.181Z vite:cache [memory] /node_modules/.vite/deps/react-redux.js?v=5981b4ff
2025-08-05T02:35:43.181Z vite:time 0.26ms /node_modules/.vite/deps/react-redux.js?v=5981b4ff
2025-08-05T02:35:43.822Z vite:cache [memory] /node_modules/.vite/deps/antd.js?v=5981b4ff
2025-08-05T02:35:43.822Z vite:time 0.38ms /node_modules/.vite/deps/antd.js?v=5981b4ff
2025-08-05T02:35:43.834Z vite:cache [memory] /src/store/index.ts
2025-08-05T02:35:43.834Z vite:time 0.37ms /src/store/index.ts
2025-08-05T02:35:43.834Z vite:cache [memory] /src/App.tsx
2025-08-05T02:35:43.834Z vite:time 0.18ms /src/App.tsx
2025-08-05T02:35:43.835Z vite:cache [memory] /node_modules/.vite/deps/antd_locale_zh_CN.js?v=5981b4ff
2025-08-05T02:35:43.835Z vite:time 0.18ms /node_modules/.vite/deps/antd_locale_zh_CN.js?v=5981b4ff
2025-08-05T02:35:48.196Z vite:cache [memory] /node_modules/.vite/deps/react-router-dom.js?v=5981b4ff
2025-08-05T02:35:48.196Z vite:time 0.25ms /node_modules/.vite/deps/react-router-dom.js?v=5981b4ff
2025-08-05T02:35:48.199Z vite:cache [memory] /src/components/AppInitializer.tsx
2025-08-05T02:35:48.200Z vite:time 0.21ms /src/components/AppInitializer.tsx
2025-08-05T02:35:48.200Z vite:cache [memory] /src/styles/index.css
2025-08-05T02:35:48.200Z vite:time 0.22ms /src/styles/index.css
2025-08-05T02:35:48.202Z vite:cache [memory] /node_modules/vite/dist/client/env.mjs
2025-08-05T02:35:48.202Z vite:time 0.18ms /node_modules/vite/dist/client/env.mjs
2025-08-05T02:35:48.211Z vite:cache [memory] /node_modules/.vite/deps/react_jsx-dev-runtime.js?v=5981b4ff
2025-08-05T02:35:48.211Z vite:time 0.29ms /node_modules/.vite/deps/react_jsx-dev-runtime.js?v=5981b4ff
2025-08-05T02:35:48.943Z vite:cache [memory] /node_modules/.vite/deps/chunk-ROME4SDB.js?v=5981b4ff
2025-08-05T02:35:48.943Z vite:time 0.38ms /node_modules/.vite/deps/chunk-ROME4SDB.js?v=5981b4ff
2025-08-05T02:35:48.944Z vite:cache [memory] /node_modules/.vite/deps/chunk-LXGCQ6UQ.js?v=5981b4ff
2025-08-05T02:35:48.944Z vite:time 0.12ms /node_modules/.vite/deps/chunk-LXGCQ6UQ.js?v=5981b4ff
2025-08-05T02:35:48.944Z vite:cache [memory] /node_modules/.vite/deps/chunk-HJ4OHFNV.js?v=5981b4ff
2025-08-05T02:35:48.944Z vite:time 0.10ms /node_modules/.vite/deps/chunk-HJ4OHFNV.js?v=5981b4ff
2025-08-05T02:35:48.944Z vite:cache [memory] /node_modules/.vite/deps/chunk-ZRNALROW.js?v=5981b4ff
2025-08-05T02:35:48.944Z vite:time 0.08ms /node_modules/.vite/deps/chunk-ZRNALROW.js?v=5981b4ff
2025-08-05T02:35:48.947Z vite:cache [memory] /node_modules/.vite/deps/chunk-J2UTGV2I.js?v=5981b4ff
2025-08-05T02:35:48.947Z vite:time 0.15ms /node_modules/.vite/deps/chunk-J2UTGV2I.js?v=5981b4ff
2025-08-05T02:35:49.291Z vite:cache [memory] /node_modules/.vite/deps/chunk-SNXB62YR.js?v=5981b4ff
2025-08-05T02:35:49.291Z vite:time 0.34ms /node_modules/.vite/deps/chunk-SNXB62YR.js?v=5981b4ff
2025-08-05T02:35:49.658Z vite:cache [memory] /node_modules/.vite/deps/@reduxjs_toolkit.js?v=5981b4ff
2025-08-05T02:35:49.658Z vite:time 0.30ms /node_modules/.vite/deps/@reduxjs_toolkit.js?v=5981b4ff
2025-08-05T02:35:49.659Z vite:cache [memory] /node_modules/.vite/deps/@reduxjs_toolkit_query.js?v=5981b4ff
2025-08-05T02:35:49.659Z vite:time 0.14ms /node_modules/.vite/deps/@reduxjs_toolkit_query.js?v=5981b4ff
2025-08-05T02:35:49.793Z vite:cache [memory] /src/store/slices/authSlice.ts
2025-08-05T02:35:49.793Z vite:time 0.31ms /src/store/slices/authSlice.ts
2025-08-05T02:35:49.819Z vite:cache [memory] /src/store/slices/uiSlice.ts
2025-08-05T02:35:49.819Z vite:time 0.37ms /src/store/slices/uiSlice.ts
2025-08-05T02:35:49.820Z vite:cache [memory] /src/store/slices/gameSlice.ts
2025-08-05T02:35:49.820Z vite:time 0.27ms /src/store/slices/gameSlice.ts
2025-08-05T02:35:49.992Z vite:cache [memory] /src/store/hooks.ts
2025-08-05T02:35:49.993Z vite:time 0.36ms /src/store/hooks.ts
2025-08-05T02:35:49.993Z vite:cache [memory] /src/store/api/apiSlice.ts
2025-08-05T02:35:49.993Z vite:time 0.11ms /src/store/api/apiSlice.ts
2025-08-05T02:35:50.320Z vite:cache [memory] /src/pages/LoginPage.tsx
2025-08-05T02:35:50.321Z vite:time 0.37ms /src/pages/LoginPage.tsx
2025-08-05T02:35:50.321Z vite:cache [memory] /node_modules/.vite/deps/styled-components.js?v=5981b4ff
2025-08-05T02:35:50.321Z vite:time 0.15ms /node_modules/.vite/deps/styled-components.js?v=5981b4ff
2025-08-05T02:35:50.321Z vite:cache [memory] /node_modules/.vite/deps/framer-motion.js?v=5981b4ff
2025-08-05T02:35:50.321Z vite:time 0.09ms /node_modules/.vite/deps/framer-motion.js?v=5981b4ff
2025-08-05T02:35:50.353Z vite:cache [memory] /src/pages/GameLobbyPage.tsx
2025-08-05T02:35:50.353Z vite:time 0.42ms /src/pages/GameLobbyPage.tsx
2025-08-05T02:35:50.354Z vite:cache [memory] /src/pages/AuthCallbackPage.tsx
2025-08-05T02:35:50.354Z vite:time 0.22ms /src/pages/AuthCallbackPage.tsx
2025-08-05T02:35:51.021Z vite:cache [memory] /src/pages/WorldCreatePage.tsx
2025-08-05T02:35:51.022Z vite:time 0.44ms /src/pages/WorldCreatePage.tsx
2025-08-05T02:35:51.048Z vite:cache [memory] /src/pages/GamePage.tsx
2025-08-05T02:35:51.049Z vite:time 0.49ms /src/pages/GamePage.tsx
2025-08-05T02:35:51.130Z vite:cache [memory] /src/components/layout/AppFooter.tsx
2025-08-05T02:35:51.130Z vite:time 0.32ms /src/components/layout/AppFooter.tsx
2025-08-05T02:35:51.130Z vite:cache [memory] /src/pages/ProfilePage.tsx
2025-08-05T02:35:51.130Z vite:time 0.23ms /src/pages/ProfilePage.tsx
2025-08-05T02:35:51.131Z vite:cache [memory] /src/components/layout/AppHeader.tsx
2025-08-05T02:35:51.131Z vite:time 0.13ms /src/components/layout/AppHeader.tsx
2025-08-05T02:35:51.400Z vite:cache [memory] /src/components/DevModeIndicator.tsx
2025-08-05T02:35:51.400Z vite:time 0.40ms /src/components/DevModeIndicator.tsx
2025-08-05T02:35:51.798Z vite:cache [memory] /node_modules/.vite/deps/chunk-WPFDAWNF.js?v=5981b4ff
2025-08-05T02:35:51.798Z vite:time 0.26ms /node_modules/.vite/deps/chunk-WPFDAWNF.js?v=5981b4ff
2025-08-05T02:35:51.800Z vite:cache [memory] /node_modules/.vite/deps/chunk-JR5FH6E4.js?v=5981b4ff
2025-08-05T02:35:51.800Z vite:time 0.15ms /node_modules/.vite/deps/chunk-JR5FH6E4.js?v=5981b4ff
2025-08-05T02:35:51.801Z vite:cache [memory] /node_modules/.vite/deps/chunk-5F26ILMS.js?v=5981b4ff
2025-08-05T02:35:51.801Z vite:time 0.11ms /node_modules/.vite/deps/chunk-5F26ILMS.js?v=5981b4ff
2025-08-05T02:35:51.801Z vite:cache [memory] /node_modules/.vite/deps/chunk-GBXKZDP3.js?v=5981b4ff
2025-08-05T02:35:51.801Z vite:time 0.10ms /node_modules/.vite/deps/chunk-GBXKZDP3.js?v=5981b4ff
2025-08-05T02:35:51.801Z vite:cache [memory] /src/services/authService.ts
2025-08-05T02:35:51.801Z vite:time 0.18ms /src/services/authService.ts
2025-08-05T02:35:52.522Z vite:cache [memory] /node_modules/.vite/deps/chunk-4UNUQYEM.js?v=5981b4ff
2025-08-05T02:35:52.522Z vite:time 0.27ms /node_modules/.vite/deps/chunk-4UNUQYEM.js?v=5981b4ff
2025-08-05T02:35:52.561Z vite:cache [memory] /node_modules/.vite/deps/@ant-design_icons.js?v=5981b4ff
2025-08-05T02:35:52.561Z vite:time 0.28ms /node_modules/.vite/deps/@ant-design_icons.js?v=5981b4ff
2025-08-05T02:35:52.562Z vite:cache [memory] /node_modules/.vite/deps/@reduxjs_toolkit_query_react.js?v=5981b4ff
2025-08-05T02:35:52.562Z vite:time 0.13ms /node_modules/.vite/deps/@reduxjs_toolkit_query_react.js?v=5981b4ff
2025-08-05T02:35:52.583Z vite:cache [memory] /src/store/api/index.ts
2025-08-05T02:35:52.583Z vite:time 0.26ms /src/store/api/index.ts
2025-08-05T02:35:52.815Z vite:cache [memory] /src/components/WorldDetailModal.tsx
2025-08-05T02:35:52.815Z vite:time 0.41ms /src/components/WorldDetailModal.tsx
2025-08-05T02:35:53.145Z vite:cache [memory] /src/components/CharacterSelectionModal.tsx
2025-08-05T02:35:53.145Z vite:time 0.40ms /src/components/CharacterSelectionModal.tsx
2025-08-05T02:35:53.145Z vite:cache [memory] /src/services/index.ts
2025-08-05T02:35:53.145Z vite:time 0.11ms /src/services/index.ts
2025-08-05T02:35:53.208Z vite:cache [memory] /src/store/api/authApi.ts
2025-08-05T02:35:53.208Z vite:time 0.33ms /src/store/api/authApi.ts
2025-08-05T02:35:53.545Z vite:cache [memory] /src/store/api/worldApi.ts
2025-08-05T02:35:53.545Z vite:time 0.39ms /src/store/api/worldApi.ts
2025-08-05T02:35:53.546Z vite:cache [memory] /src/store/api/characterApi.ts
2025-08-05T02:35:53.546Z vite:time 0.22ms /src/store/api/characterApi.ts
2025-08-05T02:35:53.593Z vite:cache [memory] /src/store/api/gameInteractionApi.ts
2025-08-05T02:35:53.593Z vite:time 0.27ms /src/store/api/gameInteractionApi.ts
2025-08-05T02:35:53.888Z vite:cache [memory] /src/services/gameService.ts
2025-08-05T02:35:53.888Z vite:time 0.38ms /src/services/gameService.ts
2025-08-05T02:35:53.889Z vite:cache [memory] /src/store/api/aiApi.ts
2025-08-05T02:35:53.889Z vite:time 0.20ms /src/store/api/aiApi.ts
2025-08-05T02:35:54.381Z vite:time 0.83ms /node_modules/.vite/deps/react_jsx-dev-runtime.js.map
2025-08-05T02:35:54.389Z vite:time 0.95ms /node_modules/.vite/deps/chunk-LXGCQ6UQ.js.map
2025-08-05T02:35:54.392Z vite:time 0.33ms /node_modules/.vite/deps/react.js.map
2025-08-05T02:35:54.392Z vite:time 0.34ms /node_modules/.vite/deps/chunk-ROME4SDB.js.map
2025-08-05T02:35:54.396Z vite:time 0.34ms /node_modules/.vite/deps/react-dom_client.js.map
2025-08-05T02:35:54.744Z vite:proxy /api/health -> http://localhost:8080
(node:3208742) [DEP0060] DeprecationWarning: The `util._extend` API is deprecated. Please use Object.assign() instead.
(Use `node --trace-deprecation ...` to show where the warning was created)
🔄 [PROXY REQ] GET /api/health → http://localhost:8080/api/health
✅ [PROXY RES] GET /api/health ← 200
2025-08-05T02:35:54.750Z vite:time 6.21ms /api/health
2025-08-05T02:35:54.822Z vite:proxy /api/health -> http://localhost:8080
🔄 [PROXY REQ] GET /api/health → http://localhost:8080/api/health
✅ [PROXY RES] GET /api/health ← 200
2025-08-05T02:35:54.824Z vite:time 2.21ms /api/health
2025-08-05T02:35:54.825Z vite:time 2.25ms /node_modules/.vite/deps/react-redux.js.map
2025-08-05T02:35:54.839Z vite:time 17.78ms /node_modules/.vite/deps/chunk-ZRNALROW.js.map
2025-08-05T02:35:54.849Z vite:time 2.21ms /node_modules/.vite/deps/chunk-J2UTGV2I.js.map
2025-08-05T02:35:55.394Z vite:time 0.43ms /node_modules/.vite/deps/chunk-HJ4OHFNV.js.map
2025-08-05T02:35:56.119Z vite:time 6.63ms /node_modules/.vite/deps/chunk-JR5FH6E4.js.map
2025-08-05T02:35:56.181Z vite:time 69.71ms /node_modules/.vite/deps/chunk-WPFDAWNF.js.map
2025-08-05T02:35:56.274Z vite:time 162.63ms /node_modules/.vite/deps/antd.js.map
2025-08-05T02:35:56.688Z vite:time 0.56ms /node_modules/.vite/deps/chunk-5F26ILMS.js.map
2025-08-05T02:35:57.853Z vite:proxy /api/health -> http://localhost:8080
🔄 [PROXY REQ] GET /api/health → http://localhost:8080/api/health
✅ [PROXY RES] GET /api/health ← 200
2025-08-05T02:35:57.855Z vite:time 1.89ms /api/health
2025-08-05T02:35:59.051Z vite:proxy /api/health -> http://localhost:8080
🔄 [PROXY REQ] GET /api/health → http://localhost:8080/api/health
✅ [PROXY RES] GET /api/health ← 200
2025-08-05T02:35:59.058Z vite:time 6.61ms /api/health
2025-08-05T02:35:59.058Z vite:time 6.28ms /node_modules/.vite/deps/chunk-SNXB62YR.js.map
2025-08-05T02:35:59.058Z vite:time 6.14ms /manifest.json
2025-08-05T02:35:59.064Z vite:time 5.99ms /node_modules/.vite/deps/react-router-dom.js.map
2025-08-05T02:35:59.066Z vite:time 6.17ms /node_modules/.vite/deps/antd_locale_zh_CN.js.map
2025-08-05T02:35:59.392Z vite:time 4.00ms /node_modules/.vite/deps/chunk-GBXKZDP3.js.map
2025-08-05T02:35:59.751Z vite:time 2.16ms /node_modules/.vite/deps/chunk-4UNUQYEM.js.map
2025-08-05T02:35:59.753Z vite:time 0.72ms /node_modules/.vite/deps/@reduxjs_toolkit_query_react.js.map
2025-08-05T02:35:59.921Z vite:time 1.25ms /node_modules/.vite/deps/styled-components.js.map
2025-08-05T02:36:00.381Z vite:time 0.27ms /node_modules/.vite/deps/@ant-design_icons.js.map
2025-08-05T02:36:00.387Z vite:time 6.60ms /node_modules/.vite/deps/framer-motion.js.map
2025-08-05T02:36:00.525Z vite:time 0.34ms /node_modules/.vite/deps/@reduxjs_toolkit.js.map
2025-08-05T02:36:01.122Z vite:time 0.34ms /node_modules/.vite/deps/@reduxjs_toolkit_query.js.map
