2025/08/05 02:34:01 🐛 [DEBUG] 开发模式已启用，详细日志输出已开启
2025/08/05 02:34:01 🐛 [DEBUG] 环境变量: ENVIRONMENT=development, DEV_ENABLE_DEBUG_LOGS=true
[GIN-debug] [WARNING] Creating an Engine instance with the Logger and Recovery middleware already attached.

[GIN-debug] [WARNING] Running in "debug" mode. Switch to "release" mode in production.
 - using env:	export GIN_MODE=release
 - using code:	gin.SetMode(gin.ReleaseMode)

[GIN-debug] GET    /assets/*filepath         --> github.com/gin-gonic/gin.(*RouterGroup).createStaticHandler.func1 (5 handlers)
[GIN-debug] HEAD   /assets/*filepath         --> github.com/gin-gonic/gin.(*RouterGroup).createStaticHandler.func1 (5 handlers)
[GIN-debug] GET    /health                   --> main.setupAPIRoutes.func1 (5 handlers)
[GIN-debug] GET    /api/health               --> main.setupAPIRoutes.func1 (5 handlers)
[GIN-debug] GET    /api/v1/user/profile      --> main.getUserProfile (5 handlers)
[GIN-debug] POST   /api/v1/auth/login        --> main.mockLogin (5 handlers)
[GIN-debug] POST   /api/v1/auth/logout       --> main.mockLogout (5 handlers)
[GIN-debug] GET    /api/v1/worlds            --> main.getWorlds (5 handlers)
[GIN-debug] POST   /api/v1/worlds            --> main.createWorld (5 handlers)
[GIN-debug] GET    /api/v1/worlds/:id        --> main.getWorld (5 handlers)
[GIN-debug] GET    /api/v1/game/my-worlds    --> main.getMyWorlds (5 handlers)
[GIN-debug] GET    /api/v1/game/public-worlds --> main.getPublicWorlds (5 handlers)
[GIN-debug] POST   /api/v1/game/worlds       --> main.createWorld (5 handlers)
[GIN-debug] PUT    /api/v1/game/worlds/:id   --> main.updateWorld (5 handlers)
[GIN-debug] DELETE /api/v1/game/worlds/:id   --> main.deleteWorld (5 handlers)
[GIN-debug] GET    /api/v1/game/my-characters --> main.getMyCharacters (5 handlers)
[GIN-debug] POST   /api/v1/game/characters   --> main.createCharacter (5 handlers)
[GIN-debug] GET    /api/v1/game/characters/:id --> main.getCharacter (5 handlers)
[GIN-debug] GET    /api/v1/game/world/:worldId/characters --> main.getWorldCharacters (5 handlers)
[GIN-debug] GET    /api/v1/games/:worldId/status --> main.getGameStatus (5 handlers)
[GIN-debug] POST   /api/v1/games/:worldId/actions --> main.performAction (5 handlers)
[GIN-debug] POST   /api/v1/ai/generate/scene --> main.generateScene (5 handlers)
[GIN-debug] GET    /api/v1/ai/interactions/history --> main.getInteractionHistory (5 handlers)
[GIN-debug] GET    /api/v1/ai/stats/token-usage --> main.getTokenUsageStats (5 handlers)
[GIN-debug] GET    /api/v1/dev/test          --> main.setupAPIRoutes.func2 (5 handlers)
2025/08/05 02:34:01 🚀 简化版AI文本游戏服务器启动在端口 8080
2025/08/05 02:34:01 🌐 前端应用: http://localhost:8080
2025/08/05 02:34:01 🔧 API文档: http://localhost:8080/api/v1
2025/08/05 02:34:01 ❤️  健康检查: http://localhost:8080/health
2025/08/05 02:34:01 🐛 [DEBUG] 调试模式已启用，将显示详细的请求日志
2025/08/05 02:34:01 🐛 [DEBUG] 示例世界数据已初始化，共 3 个世界
[GIN-debug] [WARNING] You trusted all proxies, this is NOT safe. We recommend you to set a value.
Please check https://pkg.go.dev/github.com/gin-gonic/gin#readme-don-t-trust-all-proxies for details.
[GIN-debug] Listening and serving HTTP on :8080
🌐 [2025-08-05 02:34:03] GET /health 127.0.0.1 200 33.725µs HTTP/1.1 | 用户代理: curl/7.88.1
[GIN] 2025/08/05 - 02:34:03 | 200 |      52.101µs |       127.0.0.1 | GET      "/health"
🌐 [2025-08-05 02:34:03] GET /health 127.0.0.1 200 19.708µs HTTP/1.1 | 用户代理: curl/7.88.1
[GIN] 2025/08/05 - 02:34:03 | 200 |      29.317µs |       127.0.0.1 | GET      "/health"
🌐 [2025-08-05 02:35:54] GET /api/health 127.0.0.1 200 288.6µs HTTP/1.1 | 用户代理: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36 Edg/138.0.0.0
[GIN] 2025/08/05 - 02:35:54 | 200 |     320.933µs |       127.0.0.1 | GET      "/api/health"
🌐 [2025-08-05 02:35:54] GET /api/health 127.0.0.1 200 39.587µs HTTP/1.1 | 用户代理: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36 Edg/138.0.0.0
[GIN] 2025/08/05 - 02:35:54 | 200 |      58.915µs |       127.0.0.1 | GET      "/api/health"
🌐 [2025-08-05 02:35:57] GET /api/health 127.0.0.1 200 28.996µs HTTP/1.1 | 用户代理: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36 Edg/138.0.0.0
[GIN] 2025/08/05 - 02:35:57 | 200 |      44.517µs |       127.0.0.1 | GET      "/api/health"
🌐 [2025-08-05 02:35:59] GET /api/health 127.0.0.1 200 33.064µs HTTP/1.1 | 用户代理: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36 Edg/138.0.0.0
[GIN] 2025/08/05 - 02:35:59 | 200 |      56.509µs |       127.0.0.1 | GET      "/api/health"
🌐 [2025-08-05 02:35:59] OPTIONS /api/v1/game/public-worlds?page=1&limit=20 127.0.0.1 204 15.571µs HTTP/1.1 | 用户代理: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36 Edg/138.0.0.0
[GIN] 2025/08/05 - 02:35:59 | 204 |      30.308µs |       127.0.0.1 | OPTIONS  "/api/v1/game/public-worlds?page=1&limit=20"
🌐 [2025-08-05 02:35:59] OPTIONS /api/v1/game/my-worlds?page=1&limit=10 127.0.0.1 204 8.817µs HTTP/1.1 | 用户代理: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36 Edg/138.0.0.0
[GIN] 2025/08/05 - 02:35:59 | 204 |       15.68µs |       127.0.0.1 | OPTIONS  "/api/v1/game/my-worlds?page=1&limit=10"
🌐 [2025-08-05 02:36:00] GET /api/v1/game/my-worlds?page=1&limit=10 127.0.0.1 200 136.294µs HTTP/1.1 | 用户代理: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36 Edg/138.0.0.0
[GIN] 2025/08/05 - 02:36:00 | 200 |     152.786µs |       127.0.0.1 | GET      "/api/v1/game/my-worlds?page=1&limit=10"
🌐 [2025-08-05 02:36:00] GET /api/v1/game/public-worlds?page=1&limit=20 127.0.0.1 200 191.521µs HTTP/1.1 | 用户代理: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36 Edg/138.0.0.0
[GIN] 2025/08/05 - 02:36:00 | 200 |     212.563µs |       127.0.0.1 | GET      "/api/v1/game/public-worlds?page=1&limit=20"
🌐 [2025-08-05 02:36:09] OPTIONS /api/v1/ai/generate/scene 127.0.0.1 204 4.669µs HTTP/1.1 | 用户代理: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36 Edg/138.0.0.0
[GIN] 2025/08/05 - 02:36:09 | 204 |      27.624µs |       127.0.0.1 | OPTIONS  "/api/v1/ai/generate/scene"
🌐 [2025-08-05 02:36:10] POST /api/v1/ai/generate/scene 127.0.0.1 200 101.116µs HTTP/1.1 | 用户代理: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36 Edg/138.0.0.0
[GIN] 2025/08/05 - 02:36:10 | 200 |     124.732µs |       127.0.0.1 | POST     "/api/v1/ai/generate/scene"
