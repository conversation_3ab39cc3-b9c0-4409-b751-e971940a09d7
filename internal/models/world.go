package models

import (
	"time"

	"github.com/google/uuid"
	"gorm.io/gorm"
)

// World 游戏世界模型
type World struct {
	ID             uuid.UUID      `json:"id" gorm:"type:uuid;primary_key;default:gen_random_uuid()"`
	Name           string         `json:"name" gorm:"not null;size:200"`
	Description    *string        `json:"description"`
	CreatorID      uuid.UUID      `json:"creator_id" gorm:"type:uuid;not null;index"`
	WorldConfig    JSON           `json:"world_config" gorm:"type:jsonb;not null"`
	WorldState     JSON           `json:"world_state" gorm:"type:jsonb;not null"`
	Status         string         `json:"status" gorm:"default:'active';index"`
	IsPublic       bool           `json:"is_public" gorm:"default:false;index"`
	MaxPlayers     int            `json:"max_players" gorm:"default:10"`
	CurrentPlayers int            `json:"current_players" gorm:"default:0"`
	GameTime       int64          `json:"game_time" gorm:"default:0"` // 游戏内时间(分钟)
	CreatedAt      time.Time      `json:"created_at" gorm:"index"`
	UpdatedAt      time.Time      `json:"updated_at"`
	DeletedAt      gorm.DeletedAt `json:"deleted_at,omitempty" gorm:"index"`

	// 关联
	Creator    User        `json:"creator,omitempty" gorm:"foreignKey:CreatorID"`
	Scenes     []Scene     `json:"scenes,omitempty" gorm:"foreignKey:WorldID"`
	Characters []Character `json:"characters,omitempty" gorm:"foreignKey:WorldID"`
	Entities   []Entity    `json:"entities,omitempty" gorm:"foreignKey:WorldID"`
	Events     []Event     `json:"events,omitempty" gorm:"foreignKey:WorldID"`
}

// WorldConfig 世界配置结构
type WorldConfig struct {
	TimeRate         float64                `json:"time_rate"`           // 时间倍率
	TickInterval     int                    `json:"tick_interval"`       // 心跳间隔(秒)
	MaxMemoryPerChar int                    `json:"max_memory_per_char"` // 每个角色最大记忆数
	Rules            map[string]interface{} `json:"rules"`               // 自定义规则
	Theme            string                 `json:"theme"`               // 世界主题
	Difficulty       string                 `json:"difficulty"`          // 难度等级
	Language         string                 `json:"language"`            // 语言设置
}

// WorldState 世界状态结构
type WorldState struct {
	CurrentTick     int64                  `json:"current_tick"`     // 当前心跳数
	LastTickAt      time.Time              `json:"last_tick_at"`     // 最后心跳时间
	ActiveEvents    []uuid.UUID            `json:"active_events"`    // 活跃事件ID列表
	GlobalVariables map[string]interface{} `json:"global_variables"` // 全局变量
	Weather         map[string]interface{} `json:"weather"`          // 天气状态
	Season          string                 `json:"season"`           // 季节
	WorldGoals      []uuid.UUID            `json:"world_goals"`      // 世界目标ID列表
}

// TableName 指定表名
func (World) TableName() string {
	return "worlds"
}

// BeforeCreate GORM钩子 - 创建前
func (w *World) BeforeCreate(tx *gorm.DB) error {
	if w.ID == uuid.Nil {
		w.ID = uuid.New()
	}

	// 设置默认配置
	if len(w.WorldConfig) == 0 {
		w.WorldConfig = JSON{
			"time_rate":           1.0,
			"tick_interval":       30,
			"max_memory_per_char": 100,
			"rules":               map[string]interface{}{},
			"theme":               "fantasy",
			"difficulty":          "normal",
			"language":            "zh-CN",
		}
	}

	// 设置默认状态
	if len(w.WorldState) == 0 {
		w.WorldState = JSON{
			"current_tick":     0,
			"last_tick_at":     time.Now(),
			"active_events":    []uuid.UUID{},
			"global_variables": map[string]interface{}{},
			"weather":          map[string]interface{}{"type": "clear", "temperature": 20},
			"season":           "spring",
			"world_goals":      []uuid.UUID{},
		}
	}

	return nil
}

// AfterCreate GORM钩子 - 创建后
func (w *World) AfterCreate(tx *gorm.DB) error {
	// 更新创建者的统计信息
	var stats UserStats
	err := tx.Where("user_id = ?", w.CreatorID).First(&stats).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			// 如果用户统计不存在，创建一个新的
			stats = UserStats{
				UserID:        w.CreatorID.String(),
				WorldsCreated: 1,
			}
			return tx.Create(&stats).Error
		}
		return err
	}
	return stats.IncrementWorldsCreated(tx)
}

// IsActive 检查世界是否活跃
func (w *World) IsActive() bool {
	return w.Status == "active"
}

// IsFull 检查世界是否已满
func (w *World) IsFull() bool {
	return w.CurrentPlayers >= w.MaxPlayers
}

// CanJoin 检查是否可以加入世界
func (w *World) CanJoin() bool {
	return w.IsActive() && !w.IsFull()
}

// AddPlayer 添加玩家
func (w *World) AddPlayer(tx *gorm.DB) error {
	if w.IsFull() {
		return gorm.ErrInvalidData
	}

	w.CurrentPlayers++
	return tx.Model(w).Update("current_players", w.CurrentPlayers).Error
}

// RemovePlayer 移除玩家
func (w *World) RemovePlayer(tx *gorm.DB) error {
	if w.CurrentPlayers <= 0 {
		return nil
	}

	w.CurrentPlayers--
	return tx.Model(w).Update("current_players", w.CurrentPlayers).Error
}

// UpdateGameTime 更新游戏时间
func (w *World) UpdateGameTime(tx *gorm.DB, minutes int64) error {
	w.GameTime += minutes
	return tx.Model(w).Update("game_time", w.GameTime).Error
}

// GetTimeRate 获取时间倍率
func (w *World) GetTimeRate() float64 {
	if rate, ok := w.WorldConfig["time_rate"].(float64); ok {
		return rate
	}
	return 1.0
}

// GetTickInterval 获取心跳间隔
func (w *World) GetTickInterval() int {
	if interval, ok := w.WorldConfig["tick_interval"].(float64); ok {
		return int(interval)
	}
	return 30
}

// GetMaxMemoryPerChar 获取每个角色最大记忆数
func (w *World) GetMaxMemoryPerChar() int {
	if maxMemory, ok := w.WorldConfig["max_memory_per_char"].(float64); ok {
		return int(maxMemory)
	}
	return 100
}

// UpdateWorldState 更新世界状态
func (w *World) UpdateWorldState(tx *gorm.DB, updates map[string]interface{}) error {
	// 合并更新
	for key, value := range updates {
		w.WorldState[key] = value
	}

	return tx.Model(w).Update("world_state", w.WorldState).Error
}

// GetCurrentTick 获取当前心跳数
func (w *World) GetCurrentTick() int64 {
	if tick, ok := w.WorldState["current_tick"].(float64); ok {
		return int64(tick)
	}
	return 0
}

// IncrementTick 增加心跳数
func (w *World) IncrementTick(tx *gorm.DB) error {
	currentTick := w.GetCurrentTick() + 1
	updates := map[string]interface{}{
		"current_tick": currentTick,
		"last_tick_at": time.Now(),
	}
	return w.UpdateWorldState(tx, updates)
}

// AddActiveEvent 添加活跃事件
func (w *World) AddActiveEvent(tx *gorm.DB, eventID uuid.UUID) error {
	activeEvents := w.GetActiveEvents()

	// 检查是否已存在
	for _, id := range activeEvents {
		if id == eventID {
			return nil
		}
	}

	activeEvents = append(activeEvents, eventID)
	return w.UpdateWorldState(tx, map[string]interface{}{
		"active_events": activeEvents,
	})
}

// RemoveActiveEvent 移除活跃事件
func (w *World) RemoveActiveEvent(tx *gorm.DB, eventID uuid.UUID) error {
	activeEvents := w.GetActiveEvents()

	// 过滤掉指定事件
	filtered := make([]uuid.UUID, 0, len(activeEvents))
	for _, id := range activeEvents {
		if id != eventID {
			filtered = append(filtered, id)
		}
	}

	return w.UpdateWorldState(tx, map[string]interface{}{
		"active_events": filtered,
	})
}

// GetActiveEvents 获取活跃事件列表
func (w *World) GetActiveEvents() []uuid.UUID {
	if events, ok := w.WorldState["active_events"].([]interface{}); ok {
		result := make([]uuid.UUID, 0, len(events))
		for _, e := range events {
			if eventStr, ok := e.(string); ok {
				if eventID, err := uuid.Parse(eventStr); err == nil {
					result = append(result, eventID)
				}
			}
		}
		return result
	}
	return []uuid.UUID{}
}

// SetGlobalVariable 设置全局变量
func (w *World) SetGlobalVariable(tx *gorm.DB, key string, value interface{}) error {
	globalVars := w.GetGlobalVariables()
	globalVars[key] = value

	return w.UpdateWorldState(tx, map[string]interface{}{
		"global_variables": globalVars,
	})
}

// GetGlobalVariable 获取全局变量
func (w *World) GetGlobalVariable(key string) interface{} {
	globalVars := w.GetGlobalVariables()
	return globalVars[key]
}

// GetGlobalVariables 获取所有全局变量
func (w *World) GetGlobalVariables() map[string]interface{} {
	if vars, ok := w.WorldState["global_variables"].(map[string]interface{}); ok {
		return vars
	}
	return make(map[string]interface{})
}
