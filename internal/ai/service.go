package ai

import (
	"bytes"
	"context"
	"database/sql"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"strings"
	"time"

	"ai-text-game-iam-npc/internal/config"
	"ai-text-game-iam-npc/internal/models"
	"ai-text-game-iam-npc/pkg/logger"

	"github.com/google/uuid"
	"gorm.io/gorm"
)

// WindmillClient Windmill结构化文本接口客户端
type WindmillClient struct {
	baseURL    string
	workspace  string
	token      string
	client     *http.Client
	logger     logger.Logger
	maxRetries int
	retryDelay time.Duration
}

// NewWindmillClient 创建Windmill客户端
func NewWindmillClient(cfg *config.Config, log logger.Logger) *WindmillClient {
	return &WindmillClient{
		baseURL:    cfg.AI.BaseURL,
		workspace:  cfg.AI.Windmill.Workspace,
		token:      cfg.AI.Token,
		client:     &http.Client{Timeout: cfg.AI.Timeout},
		logger:     log,
		maxRetries: cfg.AI.MaxRetries,
		retryDelay: cfg.AI.RetryDelay,
	}
}

// WindmillRequest Windmill API请求结构
type WindmillRequest struct {
	Model             string                 `json:"model"`                       // 模型名称，必填
	Prompt            string                 `json:"prompt"`                      // 主提示词，必填
	SystemInstruction string                 `json:"system_instruction,omitempty"` // 系统指令，可选
	ResponseSchema    map[string]interface{} `json:"responseSchema"`              // 响应结构定义，必填
}

// WindmillJobResponse Windmill任务提交响应
type WindmillJobResponse struct {
	JobID string `json:"job_id"` // 任务ID
}

// WindmillResultResponse Windmill结果查询响应
type WindmillResultResponse struct {
	Completed bool                   `json:"completed"` // 是否完成
	Result    map[string]interface{} `json:"result"`    // 结果数据
	Error     string                 `json:"error"`     // 错误信息
}

// GenerateStructuredContent 调用Windmill结构化文本生成接口
func (wc *WindmillClient) GenerateStructuredContent(ctx context.Context, req *WindmillRequest) (map[string]interface{}, error) {
	wc.logger.Info("开始调用Windmill结构化文本接口",
		"model", req.Model,
		"prompt_length", len(req.Prompt),
		"has_system_instruction", req.SystemInstruction != "")

	// 第一步：提交任务
	jobID, err := wc.submitJob(ctx, req)
	if err != nil {
		return nil, fmt.Errorf("提交Windmill任务失败: %w", err)
	}

	wc.logger.Info("Windmill任务提交成功", "job_id", jobID)

	// 第二步：轮询获取结果
	result, err := wc.pollResult(ctx, jobID)
	if err != nil {
		return nil, fmt.Errorf("获取Windmill任务结果失败: %w", err)
	}

	wc.logger.Info("Windmill任务完成", "job_id", jobID)
	return result, nil
}

// submitJob 提交任务到Windmill
func (wc *WindmillClient) submitJob(ctx context.Context, req *WindmillRequest) (string, error) {
	// 构建请求URL，使用配置的工作空间
	submitURL := fmt.Sprintf("%s/api/w/%s/jobs/run/p/f/gemini/js_structured_output", wc.baseURL, wc.workspace)

	// 序列化请求体
	requestBody, err := json.Marshal(req)
	if err != nil {
		return "", fmt.Errorf("序列化请求失败: %w", err)
	}

	wc.logger.Debug("Windmill任务提交请求", "url", submitURL, "body", string(requestBody))

	// 创建HTTP请求
	httpReq, err := http.NewRequestWithContext(ctx, "POST", submitURL, bytes.NewBuffer(requestBody))
	if err != nil {
		return "", fmt.Errorf("创建HTTP请求失败: %w", err)
	}

	// 设置请求头
	httpReq.Header.Set("Content-Type", "application/json")
	if wc.token != "" {
		httpReq.Header.Set("Authorization", "Bearer "+wc.token)
	}

	// 发送请求，带重试机制
	var resp *http.Response
	var lastErr error

	for attempt := 0; attempt <= wc.maxRetries; attempt++ {
		if attempt > 0 {
			wc.logger.Info("重试Windmill任务提交", "attempt", attempt, "max_retries", wc.maxRetries)
			time.Sleep(wc.retryDelay * time.Duration(attempt))
		}

		resp, lastErr = wc.client.Do(httpReq)
		if lastErr == nil && resp.StatusCode < 500 {
			break
		}

		if resp != nil {
			resp.Body.Close()
		}
	}

	if lastErr != nil {
		return "", fmt.Errorf("发送请求失败: %w", lastErr)
	}
	defer resp.Body.Close()

	// 读取响应
	responseBody, err := io.ReadAll(resp.Body)
	if err != nil {
		return "", fmt.Errorf("读取响应失败: %w", err)
	}

	wc.logger.Debug("Windmill任务提交响应", "status", resp.StatusCode, "body", string(responseBody))

	if resp.StatusCode != http.StatusOK {
		return "", fmt.Errorf("任务提交失败，状态码: %d, 响应: %s", resp.StatusCode, string(responseBody))
	}

	// 解析响应获取任务ID
	// 根据需求文档，直接返回的是UUID字符串
	jobID := strings.Trim(string(responseBody), "\"")
	if jobID == "" {
		return "", fmt.Errorf("任务ID为空")
	}

	return jobID, nil
}

// pollResult 轮询获取任务结果
func (wc *WindmillClient) pollResult(ctx context.Context, jobID string) (map[string]interface{}, error) {
	// 构建查询URL，使用配置的工作空间
	resultURL := fmt.Sprintf("%s/api/w/%s/jobs_u/completed/get_result_maybe/%s", wc.baseURL, wc.workspace, jobID)

	wc.logger.Debug("开始轮询Windmill任务结果", "job_id", jobID, "url", resultURL)

	// 设置轮询参数
	pollInterval := 1 * time.Second  // 轮询间隔
	maxPollTime := 5 * time.Minute   // 最大轮询时间
	startTime := time.Now()

	for {
		// 检查是否超时
		if time.Since(startTime) > maxPollTime {
			return nil, fmt.Errorf("轮询超时，任务ID: %s", jobID)
		}

		// 检查上下文是否被取消
		select {
		case <-ctx.Done():
			return nil, ctx.Err()
		default:
		}

		// 创建查询请求
		httpReq, err := http.NewRequestWithContext(ctx, "GET", resultURL, nil)
		if err != nil {
			return nil, fmt.Errorf("创建查询请求失败: %w", err)
		}

		// 设置请求头
		if wc.token != "" {
			httpReq.Header.Set("Authorization", "Bearer "+wc.token)
		}

		// 发送查询请求
		resp, err := wc.client.Do(httpReq)
		if err != nil {
			wc.logger.Warn("查询任务结果失败，将重试", "error", err, "job_id", jobID)
			time.Sleep(pollInterval)
			continue
		}

		// 读取响应
		responseBody, err := io.ReadAll(resp.Body)
		resp.Body.Close()
		if err != nil {
			wc.logger.Warn("读取查询响应失败，将重试", "error", err, "job_id", jobID)
			time.Sleep(pollInterval)
			continue
		}

		wc.logger.Debug("Windmill任务查询响应", "status", resp.StatusCode, "body", string(responseBody))

		if resp.StatusCode != http.StatusOK {
			wc.logger.Warn("查询请求失败，将重试", "status", resp.StatusCode, "response", string(responseBody))
			time.Sleep(pollInterval)
			continue
		}

		// 解析响应
		var resultResp WindmillResultResponse
		if err := json.Unmarshal(responseBody, &resultResp); err != nil {
			wc.logger.Warn("解析查询响应失败，将重试", "error", err, "response", string(responseBody))
			time.Sleep(pollInterval)
			continue
		}

		// 检查是否完成
		if !resultResp.Completed {
			wc.logger.Debug("任务尚未完成，继续轮询", "job_id", jobID)
			time.Sleep(pollInterval)
			continue
		}

		// 检查是否有错误
		if resultResp.Error != "" {
			return nil, fmt.Errorf("Windmill任务执行失败: %s", resultResp.Error)
		}

		// 返回结果
		wc.logger.Info("Windmill任务执行成功", "job_id", jobID)
		return resultResp.Result, nil
	}
}

// Service AI服务
type Service struct {
	config         *config.Config
	db             *gorm.DB
	client         *http.Client
	windmillClient *WindmillClient
	logger         logger.Logger
}

// NewService 创建AI服务
func NewService(cfg *config.Config, db *gorm.DB, log logger.Logger) *Service {
	return &Service{
		config: cfg,
		db:     db,
		client: &http.Client{
			Timeout: cfg.AI.Timeout,
		},
		windmillClient: NewWindmillClient(cfg, log),
		logger:         log,
	}
}

// GenerateRequest AI生成请求
type GenerateRequest struct {
	Type           string                 `json:"type"`            // 生成类型：scene, character, event, dialogue等
	Prompt         string                 `json:"prompt"`          // 提示词
	Context        map[string]interface{} `json:"context"`         // 上下文信息
	Schema         map[string]interface{} `json:"schema"`          // 期望的响应结构
	MaxTokens      int                    `json:"max_tokens"`      // 最大token数
	Temperature    float64                `json:"temperature"`     // 温度参数
	WorldID        *uuid.UUID             `json:"world_id"`        // 世界ID
	UserID         *uuid.UUID             `json:"user_id"`         // 用户ID
}

// GenerateResponse AI生成响应
type GenerateResponse struct {
	Content      string                 `json:"content"`       // 生成的内容
	StructuredData map[string]interface{} `json:"structured_data"` // 结构化数据
	TokenUsage   int                    `json:"token_usage"`   // 使用的token数
	ResponseTime int                    `json:"response_time"` // 响应时间(毫秒)
}

// GenerateContent 生成内容
func (s *Service) GenerateContent(ctx context.Context, req *GenerateRequest) (*GenerateResponse, error) {
	startTime := time.Now()
	
	// 记录AI交互日志
	interaction := &models.AIInteraction{
		WorldID:         req.WorldID,
		UserID:          req.UserID,
		InteractionType: req.Type,
		Prompt:          req.Prompt,
		ResponseSchema:  models.JSON(req.Schema),
		Status:          "pending",
	}
	
	if err := s.db.Create(interaction).Error; err != nil {
		s.logger.Error("创建AI交互记录失败", "error", err)
	}
	
	var response *GenerateResponse
	var err error
	
	// 根据配置决定使用真实API还是Mock
	if s.config.AI.MockEnabled {
		response, err = s.generateMockContent(req)
	} else {
		response, err = s.generateRealContent(ctx, req)
	}
	
	responseTime := int(time.Since(startTime).Milliseconds())
	
	// 更新交互记录
	if err != nil {
		interaction.SetFailed(s.db, err.Error())
	} else {
		responseJSON, _ := json.Marshal(response.StructuredData)
		interaction.SetCompleted(s.db, string(responseJSON), response.TokenUsage, responseTime)
	}
	
	if response != nil {
		response.ResponseTime = responseTime
	}
	
	return response, err
}

// generateRealContent 调用真实的Windmill结构化文本API生成内容
func (s *Service) generateRealContent(ctx context.Context, req *GenerateRequest) (*GenerateResponse, error) {
	s.logger.Info("开始调用Windmill结构化文本API",
		"type", req.Type,
		"world_id", req.WorldID,
		"prompt_length", len(req.Prompt))

	// 构建Windmill请求
	windmillReq := &WindmillRequest{
		Model:             s.getModelForType(req.Type),
		Prompt:            req.Prompt,
		SystemInstruction: s.buildSystemPrompt(req.Type),
		ResponseSchema:    s.buildResponseSchema(req.Type, req.Schema),
	}

	// 调用Windmill结构化文本接口
	result, err := s.windmillClient.GenerateStructuredContent(ctx, windmillReq)
	if err != nil {
		s.logger.Error("Windmill结构化文本API调用失败", "error", err)
		return nil, fmt.Errorf("调用Windmill API失败: %w", err)
	}

	// 构建响应
	response := &GenerateResponse{
		StructuredData: result,
		TokenUsage:     s.estimateTokenUsage(req.Prompt, result), // 估算token使用量
	}

	// 提取内容文本
	if content, ok := s.extractContentFromResult(result); ok {
		response.Content = content
	} else {
		// 如果无法提取内容，使用结构化数据的JSON字符串
		if contentBytes, err := json.Marshal(result); err == nil {
			response.Content = string(contentBytes)
		}
	}

	s.logger.Info("Windmill结构化文本API调用成功",
		"type", req.Type,
		"token_usage", response.TokenUsage,
		"content_length", len(response.Content))

	return response, nil
}

// getModelForType 根据内容类型获取合适的模型
func (s *Service) getModelForType(contentType string) string {
	// 根据不同的内容类型选择合适的模型
	// 这里可以根据实际需要进行配置
	switch contentType {
	case "scene", "character", "event":
		return s.config.AI.Windmill.DefaultModel // 使用配置的默认模型
	default:
		return s.config.AI.Windmill.DefaultModel
	}
}

// buildResponseSchema 构建响应结构定义
func (s *Service) buildResponseSchema(contentType string, customSchema map[string]interface{}) map[string]interface{} {
	// 如果提供了自定义Schema，优先使用
	if customSchema != nil && len(customSchema) > 0 {
		return customSchema
	}

	// 根据内容类型构建默认的响应结构
	switch contentType {
	case "scene":
		return map[string]interface{}{
			"type": "object",
			"properties": map[string]interface{}{
				"name": map[string]interface{}{
					"type":        "string",
					"description": "场景名称",
				},
				"description": map[string]interface{}{
					"type":        "string",
					"description": "详细的场景描述，包含环境、氛围、关键特征等",
				},
				"atmosphere": map[string]interface{}{
					"type":        "string",
					"description": "场景氛围（如：神秘、温馨、紧张等）",
				},
				"key_features": map[string]interface{}{
					"type": "array",
					"items": map[string]interface{}{
						"type": "string",
					},
					"description": "场景的关键特征列表",
				},
				"possible_actions": map[string]interface{}{
					"type": "array",
					"items": map[string]interface{}{
						"type": "string",
					},
					"description": "在此场景中可能的行动列表",
				},
				"connections": map[string]interface{}{
					"type": "array",
					"items": map[string]interface{}{
						"type": "object",
						"properties": map[string]interface{}{
							"direction": map[string]interface{}{
								"type":        "string",
								"description": "方向（如：北、南、东、西）",
							},
							"description": map[string]interface{}{
								"type":        "string",
								"description": "连接描述",
							},
							"scene_name": map[string]interface{}{
								"type":        "string",
								"description": "连接的场景名称",
							},
						},
						"required": []string{"direction", "description", "scene_name"},
					},
					"description": "场景连接信息",
				},
			},
			"required": []string{"name", "description", "atmosphere", "key_features", "possible_actions", "connections"},
		}

	case "character":
		return map[string]interface{}{
			"type": "object",
			"properties": map[string]interface{}{
				"name": map[string]interface{}{
					"type":        "string",
					"description": "角色名称",
				},
				"description": map[string]interface{}{
					"type":        "string",
					"description": "角色的外观和基本描述",
				},
				"personality": map[string]interface{}{
					"type": "array",
					"items": map[string]interface{}{
						"type": "string",
					},
					"description": "性格特征列表",
				},
				"background": map[string]interface{}{
					"type":        "string",
					"description": "角色背景故事",
				},
				"skills": map[string]interface{}{
					"type": "array",
					"items": map[string]interface{}{
						"type": "string",
					},
					"description": "技能列表",
				},
				"dialogue_style": map[string]interface{}{
					"type":        "string",
					"description": "对话风格描述",
				},
				"motivations": map[string]interface{}{
					"type": "array",
					"items": map[string]interface{}{
						"type": "string",
					},
					"description": "动机列表",
				},
			},
			"required": []string{"name", "description", "personality", "background", "skills", "dialogue_style", "motivations"},
		}

	case "event":
		return map[string]interface{}{
			"type": "object",
			"properties": map[string]interface{}{
				"name": map[string]interface{}{
					"type":        "string",
					"description": "事件名称",
				},
				"description": map[string]interface{}{
					"type":        "string",
					"description": "事件详细描述",
				},
				"type": map[string]interface{}{
					"type":        "string",
					"description": "事件类型（如：random、plot、character、environmental）",
				},
				"priority": map[string]interface{}{
					"type":        "integer",
					"description": "事件优先级（1-10的数字）",
					"minimum":     1,
					"maximum":     10,
				},
				"duration": map[string]interface{}{
					"type":        "integer",
					"description": "事件持续时间（分钟）",
					"minimum":     1,
				},
				"effects": map[string]interface{}{
					"type": "object",
					"properties": map[string]interface{}{
						"description": map[string]interface{}{
							"type":        "string",
							"description": "事件效果描述",
						},
						"consequences": map[string]interface{}{
							"type": "array",
							"items": map[string]interface{}{
								"type": "string",
							},
							"description": "事件后果列表",
						},
					},
					"required":    []string{"description", "consequences"},
					"description": "事件效果",
				},
			},
			"required": []string{"name", "description", "type", "priority", "duration", "effects"},
		}

	default:
		// 通用结构
		return map[string]interface{}{
			"type": "object",
			"properties": map[string]interface{}{
				"content": map[string]interface{}{
					"type":        "string",
					"description": "生成的内容",
				},
				"description": map[string]interface{}{
					"type":        "string",
					"description": "内容描述",
				},
			},
			"required": []string{"content", "description"},
		}
	}
}

// extractContentFromResult 从结构化结果中提取内容文本
func (s *Service) extractContentFromResult(result map[string]interface{}) (string, bool) {
	// 尝试提取描述字段作为主要内容
	if description, ok := result["description"].(string); ok && description != "" {
		return description, true
	}

	// 尝试提取名称字段
	if name, ok := result["name"].(string); ok && name != "" {
		return name, true
	}

	// 尝试提取内容字段
	if content, ok := result["content"].(string); ok && content != "" {
		return content, true
	}

	return "", false
}

// estimateTokenUsage 估算token使用量
func (s *Service) estimateTokenUsage(prompt string, result map[string]interface{}) int {
	// 简单的token估算：大约每4个字符为1个token
	promptTokens := len(prompt) / 4

	// 估算结果的token数
	resultBytes, _ := json.Marshal(result)
	resultTokens := len(resultBytes) / 4

	// 返回总估算token数，加上一些系统开销
	return promptTokens + resultTokens + 50
}

// 注意：旧的buildWindmillRequest方法已被删除，现在使用新的Windmill结构化文本接口

// buildSystemPrompt 根据内容类型构建系统提示
func (s *Service) buildSystemPrompt(contentType string) string {
	basePrompt := "你是一个专业的AI文本游戏内容生成助手。请根据用户的要求生成高质量的游戏内容，并以JSON格式返回结果。"

	switch contentType {
	case "scene":
		return basePrompt + `

对于场景生成，请返回包含以下字段的JSON：
{
  "name": "场景名称",
  "description": "详细的场景描述，包含环境、氛围、关键特征等",
  "atmosphere": "场景氛围（如：神秘、温馨、紧张等）",
  "key_features": ["关键特征1", "关键特征2"],
  "possible_actions": ["可能的行动1", "可能的行动2"],
  "connections": [
    {
      "direction": "方向（如：北、南、东、西）",
      "description": "连接描述",
      "scene_name": "连接的场景名称"
    }
  ]
}`

	case "character":
		return basePrompt + `

对于角色生成，请返回包含以下字段的JSON：
{
  "name": "角色名称",
  "description": "角色的外观和基本描述",
  "personality": ["性格特征1", "性格特征2"],
  "background": "角色背景故事",
  "skills": ["技能1", "技能2"],
  "dialogue_style": "对话风格描述",
  "motivations": ["动机1", "动机2"]
}`

	case "event":
		return basePrompt + `

对于事件生成，请返回包含以下字段的JSON：
{
  "name": "事件名称",
  "description": "事件详细描述",
  "type": "事件类型（如：random、plot、character、environmental）",
  "priority": 事件优先级（1-10的数字）,
  "duration": 事件持续时间（分钟）,
  "effects": {
    "description": "事件效果描述",
    "consequences": ["后果1", "后果2"]
  }
}`

	default:
		return basePrompt + "\n\n请根据用户要求生成相应的游戏内容，并以结构化的JSON格式返回。"
	}
}

// formatContext 格式化上下文信息
func (s *Service) formatContext(context map[string]interface{}) string {
	if context == nil || len(context) == 0 {
		return ""
	}

	var parts []string
	for key, value := range context {
		if valueStr, ok := value.(string); ok && valueStr != "" {
			parts = append(parts, fmt.Sprintf("%s: %s", key, valueStr))
		}
	}

	return strings.Join(parts, "; ")
}

// buildSchemaPrompt 构建Schema提示
func (s *Service) buildSchemaPrompt(schema map[string]interface{}) string {
	if schema == nil || len(schema) == 0 {
		return ""
	}

	prompt := "请确保返回的JSON包含以下字段："
	for field, fieldType := range schema {
		prompt += fmt.Sprintf("\n- %s: %v", field, fieldType)
	}

	return prompt
}

// 注意：旧的parseWindmillResponse方法已被删除，现在直接使用Windmill结构化文本接口返回的结构化数据

// createFallbackStructuredData 创建备用的结构化数据
func (s *Service) createFallbackStructuredData(content, contentType string) map[string]interface{} {
	switch contentType {
	case "scene":
		return map[string]interface{}{
			"name":            "生成的场景",
			"description":     content,
			"atmosphere":      "未知",
			"key_features":    []string{},
			"possible_actions": []string{},
			"connections":     []interface{}{},
		}
	case "character":
		return map[string]interface{}{
			"name":           "生成的角色",
			"description":    content,
			"personality":    []string{},
			"background":     content,
			"skills":         []string{},
			"dialogue_style": "未知",
			"motivations":    []string{},
		}
	case "event":
		return map[string]interface{}{
			"name":        "生成的事件",
			"description": content,
			"type":        "random",
			"priority":    5,
			"duration":    30,
			"effects": map[string]interface{}{
				"description":   content,
				"consequences": []string{},
			},
		}
	default:
		return map[string]interface{}{
			"content":     content,
			"description": content,
		}
	}
}

// generateMockContent 生成Mock内容
func (s *Service) generateMockContent(req *GenerateRequest) (*GenerateResponse, error) {
	s.logger.Info("使用Mock模式生成AI内容", "type", req.Type)
	
	switch req.Type {
	case "scene":
		return s.generateMockScene(req)
	case "character":
		return s.generateMockCharacter(req)
	case "event":
		return s.generateMockEvent(req)
	case "dialogue":
		return s.generateMockDialogue(req)
	case "description":
		return s.generateMockDescription(req)
	default:
		return s.generateMockGeneral(req)
	}
}

// generateMockScene 生成Mock场景
func (s *Service) generateMockScene(req *GenerateRequest) (*GenerateResponse, error) {
	mockScenes := []map[string]interface{}{
		{
			"name":        "神秘森林",
			"description": "一片古老而神秘的森林，高大的树木遮天蔽日，林间弥漫着淡淡的雾气。偶尔能听到远处传来的鸟鸣声和树叶沙沙作响的声音。",
			"type":        "forest",
			"atmosphere":  "mysterious",
			"connections": map[string]string{
				"north": "山洞入口",
				"south": "村庄广场",
				"east":  "河边小径",
			},
			"entities": []string{"古老的橡树", "发光的蘑菇", "小溪"},
		},
		{
			"name":        "废弃城堡",
			"description": "一座年代久远的城堡，石墙斑驳，藤蔓缠绕。城堡内部阴暗潮湿，回音在空旷的大厅中回荡。",
			"type":        "castle",
			"atmosphere":  "eerie",
			"connections": map[string]string{
				"west":  "城堡花园",
				"north": "塔楼",
				"down":  "地下室",
			},
			"entities": []string{"破损的盔甲", "古老的画像", "蜘蛛网"},
		},
	}
	
	// 随机选择一个场景
	selectedScene := mockScenes[time.Now().Unix()%int64(len(mockScenes))]
	
	return &GenerateResponse{
		Content:        selectedScene["description"].(string),
		StructuredData: selectedScene,
		TokenUsage:     150,
	}, nil
}

// generateMockCharacter 生成Mock角色
func (s *Service) generateMockCharacter(req *GenerateRequest) (*GenerateResponse, error) {
	character := map[string]interface{}{
		"name":        "艾莉娅",
		"description": "一位年轻的精灵法师，有着银色的长发和翠绿的眼睛。",
		"type":        "npc",
		"personality": []string{"智慧", "善良", "好奇"},
		"skills":      []string{"魔法", "草药学"},
		"background":  "来自精灵王国的年轻法师。",
	}

	return &GenerateResponse{
		Content:        character["description"].(string),
		StructuredData: character,
		TokenUsage:     120,
	}, nil
}


// generateMockEvent 生成Mock事件
func (s *Service) generateMockEvent(req *GenerateRequest) (*GenerateResponse, error) {
	mockEvents := []map[string]interface{}{
		{
			"name":        "神秘商人的到来",
			"description": "一位穿着华丽长袍的神秘商人来到了村庄，他的马车上装满了奇异的物品和魔法道具。",
			"type":        "encounter",
			"priority":    5,
			"duration":    30,
			"effects": map[string]interface{}{
				"new_items":     []string{"魔法药水", "古老地图", "神秘护符"},
				"new_quests":    []string{"寻找失落的宝藏"},
				"reputation":    10,
			},
		},
		{
			"name":        "暴风雨来袭",
			"description": "天空突然乌云密布，雷声轰鸣，一场猛烈的暴风雨即将来临。所有户外活动都必须暂停。",
			"type":        "weather",
			"priority":    3,
			"duration":    60,
			"effects": map[string]interface{}{
				"weather_change": "storm",
				"visibility":     "low",
				"movement_speed": 0.5,
			},
		},
	}
	
	selectedEvent := mockEvents[time.Now().Unix()%int64(len(mockEvents))]
	
	return &GenerateResponse{
		Content:        selectedEvent["description"].(string),
		StructuredData: selectedEvent,
		TokenUsage:     100,
	}, nil
}

// generateMockDialogue 生成Mock对话
func (s *Service) generateMockDialogue(req *GenerateRequest) (*GenerateResponse, error) {
	mockDialogues := []string{
		"欢迎来到我们的村庄，陌生人。你看起来像是从很远的地方来的。",
		"这里最近发生了一些奇怪的事情，也许你能帮助我们解决这个问题。",
		"小心那片森林，据说里面住着危险的生物。",
		"你有什么需要的吗？我这里有各种各样的物品。",
		"传说中的宝藏就在那座古老的城堡里，但是没有人敢去寻找。",
	}
	
	selectedDialogue := mockDialogues[time.Now().Unix()%int64(len(mockDialogues))]
	
	return &GenerateResponse{
		Content: selectedDialogue,
		StructuredData: map[string]interface{}{
			"dialogue": selectedDialogue,
			"emotion":  "neutral",
			"intent":   "information",
		},
		TokenUsage: 50,
	}, nil
}

// generateMockDescription 生成Mock描述
func (s *Service) generateMockDescription(req *GenerateRequest) (*GenerateResponse, error) {
	descriptions := []string{
		"这是一个充满魔法和奇迹的世界，古老的传说在这里成为现实。",
		"微风轻抚过草地，带来了远方花朵的香气。",
		"夕阳西下，金色的光芒洒在大地上，一切都显得那么宁静美好。",
		"古老的石碑上刻着神秘的符文，似乎在诉说着久远的故事。",
		"篝火在夜晚中跳跃着，温暖的光芒驱散了黑暗和寒冷。",
	}
	
	selectedDescription := descriptions[time.Now().Unix()%int64(len(descriptions))]
	
	return &GenerateResponse{
		Content: selectedDescription,
		StructuredData: map[string]interface{}{
			"description": selectedDescription,
			"mood":        "peaceful",
			"style":       "descriptive",
		},
		TokenUsage: 80,
	}, nil
}

// generateMockGeneral 生成通用Mock内容
func (s *Service) generateMockGeneral(req *GenerateRequest) (*GenerateResponse, error) {
	return &GenerateResponse{
		Content: fmt.Sprintf("这是一个关于%s的Mock响应。在实际环境中，这里会调用真实的AI API来生成内容。", req.Type),
		StructuredData: map[string]interface{}{
			"type":    req.Type,
			"mock":    true,
			"prompt":  req.Prompt,
			"context": req.Context,
		},
		TokenUsage: 75,
	}, nil
}

// GetInteractionHistory 获取AI交互历史
func (s *Service) GetInteractionHistory(worldID *uuid.UUID, userID *uuid.UUID, limit int) ([]models.AIInteraction, error) {
	var interactions []models.AIInteraction
	query := s.db.Order("created_at DESC")
	
	if worldID != nil {
		query = query.Where("world_id = ?", *worldID)
	}
	
	if userID != nil {
		query = query.Where("user_id = ?", *userID)
	}
	
	if limit > 0 {
		query = query.Limit(limit)
	}
	
	err := query.Find(&interactions).Error
	return interactions, err
}

// GetTokenUsageStats 获取token使用统计
func (s *Service) GetTokenUsageStats(worldID *uuid.UUID, userID *uuid.UUID, days int) (map[string]interface{}, error) {
	var stats struct {
		TotalInteractions int64 `json:"total_interactions"`
		TotalTokens       int   `json:"total_tokens"`
		AvgTokensPerReq   int   `json:"avg_tokens_per_request"`
	}

	query := s.db.Model(&models.AIInteraction{}).Where("status = ?", "completed")

	if worldID != nil {
		query = query.Where("world_id = ?", *worldID)
	}

	if userID != nil {
		query = query.Where("user_id = ?", *userID)
	}

	if days > 0 {
		query = query.Where("created_at >= ?", time.Now().AddDate(0, 0, -days))
	}

	// 获取总交互数
	query.Count(&stats.TotalInteractions)
	
	// 获取总token数
	var totalTokens sql.NullInt64
	query.Select("SUM(token_usage)").Scan(&totalTokens)
	stats.TotalTokens = int(totalTokens.Int64)
	
	// 计算平均值
	if stats.TotalInteractions > 0 {
		stats.AvgTokensPerReq = stats.TotalTokens / int(stats.TotalInteractions)
	}
	
	return map[string]interface{}{
		"total_interactions":     stats.TotalInteractions,
		"total_tokens":          stats.TotalTokens,
		"avg_tokens_per_request": stats.AvgTokensPerReq,
	}, nil
}
